package aml

import (
	"context"
	"fmt"

	"google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	amlPb "github.com/epifi/gamma/api/aml"
	amlVgPb "github.com/epifi/gamma/api/vendorgateway/aml"
)

func (s *Service) storeScreeningAttemptDetails(ctx context.Context, request *amlPb.ScreenCustomerRequest, vgRes *amlVgPb.ScreenCustomerResponse) (string, error) {
	result := convertToBEResult(vgRes.GetMatchStatus())
	if result == amlPb.AmlMatch_AML_MATCH_UNSPECIFIED {
		return "", fmt.Errorf("failed to convert to BE match result, VG MatchStatus: %v", vgRes.GetMatchStatus())
	}
	var attemptStatus amlPb.AmlScreeningStatus
	switch result {
	case amlPb.AmlMatch_AML_MATCH_ERROR:
		attemptStatus = amlPb.AmlScreeningStatus_AML_SCREENING_STATUS_FAILED
	default:
		attemptStatus = amlPb.AmlScreeningStatus_AML_SCREENING_STATUS_SUCCESS
	}
	attempt, err := s.screeningAttemptDao.Create(ctx, &amlPb.ScreeningAttempt{
		ActorId:                  request.GetActorId(),
		ClientRequestId:          request.GetClientRequestId(),
		Product:                  request.GetAmlProduct(),
		Vendor:                   commonvgpb.Vendor_TSS,
		CustomerDetails:          request.GetCustomerDetails(),
		Status:                   attemptStatus,
		Result:                   result,
		RejectionMessage:         vgRes.GetRejectionMessage(),
		RejectionCode:            vgRes.GetRejectionCode(),
		LastScreeningAttemptedAt: timestamppb.Now(),
		Owner:                    request.GetOwner(),
	})
	if err != nil {
		return "", err
	}
	return attempt.GetId(), nil
}

func (s *Service) storeCaseDetails(ctx context.Context, request *amlPb.ScreenCustomerRequest, vgRes *amlVgPb.ScreenCustomerResponse, screeningAttemptId string) error {
	_, err := s.amlCaseDetailsDao.Create(ctx, &amlPb.AmlCaseDetails{
		ActorId:                request.GetActorId(),
		ReviewStatus:           amlPb.ReviewStatus_REVIEW_STATUS_PENDING,
		ProductsCollection:     &amlPb.AmlProductCollection{Products: []amlPb.AmlProduct{request.GetAmlProduct()}},
		VendorCaseId:           vgRes.GetCaseId(),
		Vendor:                 commonvgpb.Vendor_TSS,
		MatchDetailsCollection: &amlPb.MatchDetailsCollection{MatchDetails: vgRes.GetMatchDetails()},
		AttemptId:              screeningAttemptId,
		Owner:                  request.GetOwner(),
	})
	if err != nil {
		return err
	}
	return nil
}
