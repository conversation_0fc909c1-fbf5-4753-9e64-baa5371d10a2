package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"

	"github.com/epifi/be-common/pkg/cfg"

	"github.com/pkg/errors"
)

var (
	once       sync.Once
	config     *Config
	err        error
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to load config")
	}

	return config, nil
}

func loadConfig() (*Config, error) {
	conf := &Config{}
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()

	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, cfg.AML_SERVICE)

	if err != nil {
		return nil, fmt.Errorf("failed to load dynamic config: %w", err)
	}

	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to refresh dymanic config: %w", err)
	}

	keyToSecret, err := cfg.LoadAllSecrets(conf.EpifiDb, nil, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, err
	}

	if err := updateDefaultConfig(conf, keyToSecret); err != nil {
		return nil, err
	}
	return conf, nil

}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointInConfig(c.EpifiDb, dbServerEndpoint)

	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	cfg.UpdateSecretValues(c.EpifiDb, nil, keyToSecret)
	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Ports.GrpcPort = intVal
	}

	if val, ok := os.LookupEnv("DB_HOST"); ok {
		c.EpifiDb.Host = val
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

type Config struct {
	Application              *Application
	Server                   *Server
	EpifiDb                  *cfg.DB
	EnableService            bool
	AWS                      *Aws
	VnCaseDecisionSubscriber *cfg.SqsSubscriber
	CaseDecisionPublisher    *cfg.SnsPublisher
	FileGenerationSubscriber *cfg.SqsSubscriber
	DbConfigMap              cfg.DbConfigMap
	Flags                    *Flags
}

type Application struct {
	Environment string
	Name        string
}

type Server struct {
	Ports        *cfg.ServerPorts
	EnablePoller bool
}

type S3 struct {
	BucketName string `iam:"s3-readwrite"`
}

type Aws struct {
	Region string
	S3     *S3
}

type Flags struct {
	// When switched on, all calls to perform Anti-Money-Laundering (AML) screening calls
	// are routed to the cloud service (Screenzaa) provided by the background-check service provider
	// instead of their in-house service hosted in Epifi's vendor-specific AWS account.
	SwitchToCloudService bool
}
