Application:
  Environment: "prod"
  Name: "vendorgateway"
  IsSecureRedis: true
  SyncWrapperTimeout: 60
  VGAuthSvcSyncWrapperTimeout: 20
  IsStatementAPIEnabled: true
  CreateDisputeURL: "https://gateway.federalbank.co.in/prod/prod/DMP/v1.0.0/createDispute"
  DisputeStatusCheckUrl: "https://gateway.federalbank.co.in/prod/prod/DMP/v1.0.0/disputeStatusCheck"
  BulkDisputeStatusCheckUrl: "https://gateway.federalbank.co.in/prod/prod/DMP/v1.0.0/bulkDisputeStatus"
  SendCorrespondenceUrl: "https://gateway.federalbank.co.in/prod/prod/DMP/v1.0.0/disputeCorrespondence"
  UploadDocumentUrl: "https://gateway.federalbank.co.in/prod/prod/DMP/v1.0.0/disputeDocument"
  ChannelQuestionnaireUrl: "https://gateway.federalbank.co.in/prod/prod/DMP/v1.0.0/channelQuestionnaire"
  AccountTransactionsUrl: "https://gateway.federalbank.co.in/prod/prod/DMP/v1.0.0/transactions"
  CreateCustomerURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/customer/creation"
  CheckCustomerStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"
  CreateLoanCustomerURL: "https://gateway.federalbank.co.in/prod/prod/loan/cif/v2.0.0/customer/creation"
  LoanCustomerCreationStatusURL: "https://gateway.federalbank.co.in/prod/prod/cif/v1.0.0/enquiry"
  CreateAccountURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/savings/account/opening"
  CheckAccountStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"
  DedupeCheckURL: "https://gateway.federalbank.co.in/prod/prod/ddupe/v3.0.0/check"
  FetchCustomerDetailsUrl: "https://gateway.federalbank.co.in:443/prod/prod/neobanking/v1.0.0/customer-details/enquire"
  EnquireBalanceURL: "https://gateway.federalbank.co.in:443/prod/prod/neobanking/v1.0.0/balanceEnq"
  EnquireVKYCStatusUrl: "https://gateway.federalbank.co.in/prod/prod/vkycenquiry/enquiry"
  CkycSearchURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/ckyc/enquiry"
  GetKycDataURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/ckyc/download"
  CreateVirtualIdURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  GetTokenURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  DeviceRegistrationURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/user-device/registration"
  SetPINURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  UPIBalanceEnquiryURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  ReqComplaintURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  ReqCheckComplaintStatusUrl: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  ValidateAddressURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  GenerateUpiOtpURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  RespAuthDetailsURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  ReqPayURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  RegisterMobileURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  ListUpiKeyUrl: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  ListAccountURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  ListAccountProviderURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  RespTxnConfirmationURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  RespValidateAddressURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  ReqCheckTxnStatusURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  ListVaeURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  ListPspURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  ReqMandateURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  RespAuthMandateURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  RespMandateConfirmationURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  RespAuthValCustURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  # TODO(Yatin to add url: https://monorail.pointz.in/p/fi-app/issues/detail?id=28501)
  ReqActivationUrl: ""
  ReqValQRUrl: ""
  GetUpiLiteURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  SyncUpiLiteInfoURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  Razorpay:
    BaseUrl: "https://api.razorpay.com"

  AclSftp:
    User: "prod-comms-acl"
    Host: "prod-sftp.epifi.in"
    Port: 22

  # Generic integration for Zenduty in onboarding service (for POC)
  ZendutyWebhookUrl: "https://events.zenduty.com/integration/cj9ui/generic/b12b8ea8-00c8-4f46-bd72-17502e7e7e96/"

  # UPI Mapper PROD API urls
  GetMapperInfoURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  RegMapperURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"
  RespMapperConfirmationURL: "https://remittance.federalbank.co.in:8804/UPIMerchantApp"

  PanProfileURL: "https://api.karza.in/v3/pan-profile"

  BureauIdUrl: "https://api.bureau.id/transactions"

  # EPAN Url to get Epan Status
  GetEPANKarzaStatusURL: "https://app.karza.in/prod/videokyc/api/v2/epan-staging"
  InhouseGetAndValidateEPANURL: "https://delta-server.epifi.in/verify/epan"

  # send vkyc data to federal for inhouse vkyc service
  SendAgentDataURL: "https://vkyc.federalbank.co.in:447/vKYCPostBack/AgentStatus"
  SendAuditorDataURL: "https://vkyc.federalbank.co.in:447/vKYCPostBack/AgentStatus"

  # ITR
  InhouseVerifyAndGetITRIntimationDetailsURL: "https://delta-server.epifi.in/verify/itr-intimation"

  #Call back urls for account setup
  CreateCustomerCallBackUrl: "https://vnotificationgw.epifi.in/openbanking/customer/create"
  CreateAccountCallBackUrl: "https://vnotificationgw.epifi.in/openbanking/account/create"

  # Liveness Service
  Veri5CheckLivenessRequestURL: "https://prod-simulator.epifi.vpc:9091/video-id-kyc/api/1.0/liveness"
  Veri5MatchFaceRequestURL: "https://prod-simulator.epifi.vpc:9091/video-id-kyc/api/1.0/faceCompare"
  KarzaCheckLivenessRequestURL: "https://liveness.epifi.in/v3/video-liveness"
  KarzaLivenessCallbackURL: "https://vnotificationgw.epifi.in/liveness/karza"
  KarzaMatchFaceRequestURL: "https://r8eve8f0w0.execute-api.ap-south-1.amazonaws.com/prod/face-similarity"
  KarzaCheckPassiveLivenessRequestURL: "https://prod-simulator.epifi.vpc:9091/v3/image-liveness"
  KarzaCheckLivenessStatusURL: "https://r8eve8f0w0.execute-api.ap-south-1.amazonaws.com/prod/video-liveness-status"
  InhouseCheckLivenessRequestURL: "https://liveness.data-prod.epifi.in/"
  InhouseMatchFaceRequestURL: "https://facematch.data-prod.epifi.in/v1/facematch"
  InhouseMatchFaceRequestURLV2: "https://liveness.data-prod.epifi.in/v1/facematch"
  UseFormMarshalForKarza: true
  UseFormMarshalForKarzaFM: true

  # Employment Service
  Employment:
    KarzaPFOTPURL: "https://api.karza.in/v2/epf-get-otp"
    KarzaPFPassbookURL: "https://api.karza.in/v2/epf-get-passbook"
    KarzaEmploymentVerificationURL: "https://api.karza.in/v2/employment-verification-advanced"
    KarzaSearchCompanyNameURL: "https://api.kscan.in/v3/employer-search-lite"
    KarzaUANLookupURL: "https://api.karza.in/v2/uan-lookup"
    KarzaEPFAuthURL: "https://testapi.karza.in/v2/epf-auth"
    KarzaEmployeeNameSearchURL: "https://api.karza.in/v2/employee-search"
    KarzaCompanyMasterLLPDataURL: "https://api.karza.in/v2/mca"
    KarzaSearchGSTINBasisPAN: "https://gst.karza.in/prod/v1/search"
    KarzaGetForm16QuarterlyURL: "https://api.karza.in/v3/tdsq"
    KarzaGetEmployerDetailsByGstinURL: "https://api.karza.in/gst/prod/v2/gst-verification"
    KarzaGetUANFromPan: "https://api.karza.in/v2/employment-verification-advanced"
    SignzyLoginURL: "https://signzy.tech/api/v2/patrons/login"
    SignzyDomainNameVerificationURL: "https://signzy.tech/api/v2/patrons/userid/domainverifications"
    KarzaFindUanByPan: "https://api.karza.in/v3/pan-uan"

  # CRM Service
  FreshdeskURI:
    Agent: "/agents"
    Ticket: "/tickets"
    FilterTicket: "/search/tickets"
    Solutions: "/solutions"
    Contacts: "/contacts"
    TicketField: "/admin/ticket_fields"
    Job: "/jobs"
    BulkUpdate: "/tickets/bulk_update"
  FreshDeskAccountConfig:
    EPIFI_TECH:
      USE_CASE_RISK_CASE_MANAGEMENT:
        ApiTokenSecretName: "EpifiTechRiskFreshdeskApiKey"
        BaseURL: "https://epifirisk.freshdesk.com/api/v2"
    FEDERAL_BANK:
      USE_CASE_RISK_CASE_MANAGEMENT:
        ApiTokenSecretName: "EpifiTechRiskFreshdeskApiKey"
        BaseURL: "https://epifirisk.freshdesk.com/api/v2"

  # Freshdesk service
  FreshdeskAgentURL: "https://ficare.freshdesk.com/api/v2/agents"
  FreshdeskTicketURL: "https://ficare.freshdesk.com/api/v2/tickets"
  FreshdeskFilterTicketURL: "https://ficare.freshdesk.com/api/v2/search/tickets"
  FreshdeskSolutionsURL: "https://ficare.freshdesk.com/api/v2/solutions"
  FreshdeskContactsURL: "https://ficare.freshdesk.com/api/v2/contacts"
  FreshdeskTicketFieldURL: "https://ficare.freshdesk.com/api/v2/admin/ticket_fields"
  CxFreshdeskTicketAttachmentsBucketName: "epifi-prod-cx-ticket-attachments"

  #Freshchat service
  FreshchatConversationURL: "https://ficare.freshchat.com/v2/conversations"
  FreshchatUserURL: "https://ficare.freshchat.com/v2/users"
  FreshchatAgentURL: "https://ficare.freshchat.com/v2/agents"

  SenseforthEventURL: "https://chatbotapi.epifi.in/getmessages"


  InhouseNameCheckUrl: "https://entity-matcher.data-prod.epifi.in/api/v1/namepairmatch"
  InhouseEmployerNameMatchUrl: "https://entity-matcher.data-prod.epifi.in/api/v1/companymatch"
  InhouseEmployerNameCategoriserUrl: "https://text-semantics.data-prod.epifi.in/v1/name_categoriser"
  InhouseBreForCCUrl: "https://iris.epifi.in/evaluator/flow/v1/flow-ccbrepolicy"

  DrivingLicenseValidationUrl: "https://api.karza.in/v3/dl"

  VoterIdValidationUrl: "https://api.karza.in/v2/voter"

  BankAccountVerificationUrl: "https://api.karza.in/v2/bankacc"

  CAMS:
    OrderFeedFileURL: "https://fundsnet.camsonline.com/ecrms/stp/fnSystematicBatchSubmitRTA_Exg.aspx"
    FATCAFileURL: "https://fundsnet.camsonline.com/ecrms/stp/fnStpSystematic_Fatca.aspx"
    ElogFileURL: "https://fundsnet.camsonline.com/ecrms/STP/StpSystematic_ELogUpload.aspx"
    OrderFeedFileStatusURL: "https://fundsnet.camsonline.com/ecrms/stp/fnsystematicBatchStatus_RTA.aspx"
    OrderFeedFileSyncURL: "https://fundsnet.camsonline.com/ecrms/stp/fnSystematicExchangeUpload_RTA.aspx"
    S3Bucket: "epifi-prod-mutualfund"
    NFTFileURL: "https://fundsnet.camsonline.com/ecrms/stp/fnStpSystematic_NF.aspx"
    GetFolioDetailsURL: "https://exws.camsonline.com/CAMSWS_RD/Services_DIST/Contact_Details"
    NomineeUpdateURL: "https://camsgw.camsonline.com/CAMSWS_GW/common/api/nomineeRegistration"
    UserCode: "EPIFIRIA"
    BrokerCode: "INA200015185"

  SmallCase:
    CreateTransactionURL: "https://gatewayapi.smallcase.com/gateway/fimoney/transaction"
    InitiateHoldingsImportURL: "https://mf-api.smallcase.com/gateway/mf/bridge"
    TriggerHoldingsImportFetchURL: "https://mf-api.smallcase.com/gateway/mf/bridge"
    MFAnalyticsURL: "https://mf-api.smallcase.com/gateway/mf/analytics"
    SmallCaseGateway: "fimoney"
    EnableNotFoundInInitHoldingsImport: true

  Tiering:
    AddSchemeChangeURL: "https://gateway.federalbank.co.in/prod/prod/digitalCredit/v1.0.0/schemeChangeAddEnq"
    EnquireSchemeChangeURL: "https://gateway.federalbank.co.in/prod/prod/digitalCredit/v1.0.0/schemeChangeAddEnq"

  MFCentral:
    GenerateTokenURL: "https://services.mfcentral.com/oauth/token"
    EncryptAndSignURL: "https://prod-nsdl-forwardsecrecy.epifi.in/mfcentral/v1/encryptAndSign"
    VerifyAndDecryptURL: "https://prod-nsdl-forwardsecrecy.epifi.in/mfcentral/v1/verifyAndDecrypt"
    UpdateFolioEmailURL: "https://services.mfcentral.com/api/client/V1/updateEmail"
    UpdateFolioMobileURL: "https://services.mfcentral.com/api/client/V1/updateMobile"
    InvestorConsentUrl: "https://services.mfcentral.com/api/client/V1/investorconsent"
    SubmitCasSummaryUrl: "https://services.mfcentral.com/api/client/V1/submitcassummaryrequest"
    GetCasDocumentUrl: "https://services.mfcentral.com/api/client/V1/getcasdocument"
    GetTransactionStatusUrl: "https://services.mfcentral.com/api/client/V1/getTransactionStatus"

  ## aa smart parser
  InHouseAAParserURL: "https://smart-parser.data-prod.epifi.in/parse"
  InHouseAABulkParserURL: "https://smart-parser.data-prod.epifi.in/bulk_parse"


  #SMS Service
  Exotel:
    URL: "https://%s:%<EMAIL>/v1/Accounts/%s/SMS"
    AccountSid: "epifi2"
    SenderId: "***********"
  Twilio:
    URL: "https://api.twilio.com/2010-04-01/Accounts/%s/Messages"
    SenderId: "***********"
  AclEpifi:
    FallbackURL: "https://dmz.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    URL: "https://push3.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    AppId: "epipalt"
    SenderId: "FiMony"
  AclFederal:
    FallbackURL: "https://dmz.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    URL: "https://push3.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    AppId: "fdaltefi"
    SenderId: "FedFib"
  AclEpifiOtp:
    FallbackURL: "https://dmzotp.aclgateway.com/OTP_ACL_Web/OtpRequestListener"
    URL: "https://otp2.aclgateway.com/OTP_ACL_Web/OtpRequestListener"
    AppId: "epifotp"
    SenderId: "FiMony"
  KaleyraFederal:
    URL: "https://api.in.kaleyra.io/alerts/api/v4"
    SenderId: "FedFiB"
  KaleyraEpifi:
    URL: "https://api.in.kaleyra.io/alerts/api/v4"
    SenderId: "FiMony"
  KaleyraEpifiNR:
    URL: "https://api.in.kaleyra.io/v1/HXIN1778099997IN/messages"
    SenderId: "FIMONY"
    CallbackProfileId: "IN_22d84710-70bb-4e30-a1b5-2359a358ef72"
  KaleyraSmsCallbackURL: "https://vnotificationgw.epifi.in/sms/callback/kaleyra/UrlListner/requestListener"
  AclWhatsapp:
    URL: "https://push.aclwhatsapp.com/pull-platform-receiver/wa/messages"
    OptInURL: "http://115.113.127.155:8085/api/v1/addoptinpost"
  KaleyraFederalCreditCard:
    URL: "https://api.in.kaleyra.io/alerts/api/v4"
    SenderId: "FedFiB"
  #Loylty Service
  Loylty:
    AuthTokenURL: "https://auth.loylty.com/v1/mtoken"
    GiftCardBookingURL: "https://egvb9.loylty.com/V2/GiftCard/Request"
    CharityBookingURL: "https://cbkb9.loylty.com/V2/InitiateV2"
    GiftCardProductListURL: "https://egvb9.loylty.com/V2/GiftCard/Products"
    GiftCardProductDetailURL: "https://egvb9.loylty.com/V2/GiftCard/Products/%s"
    CreateOrderURL: "https://ordb9.loylty.com/V2/Order"
    ConfirmOrderURL: "https://ordb9.loylty.com/V2/Order/%s/Confirm"
    GetOrderDetailsURL: "https://ordb9.loylty.com/V2/Order/%s"

  Qwikcilver:
    GetAuthorizationCodeBaseUrl: "https://extapi12.woohoo.in/oauth2/verify"
    GetAccessTokenBaseUrl: "https://extapi12.woohoo.in/oauth2/token"
    CreateOrderBaseUrl: "https://extapi12.woohoo.in/rest/v3/orders"
    GetActivatedCardDetailsBaseUrl: "https://extapi12.woohoo.in/rest/v3/order/%s/cards"
    GetCategoryDetailsBaseUrl: "https://extapi12.woohoo.in/rest/v3/catalog/categories"
    GetOrderStatusBaseUrl: "https://extapi12.woohoo.in/rest/v3/order/%s/status"
    GetProductDetailsBaseUrl: "https://extapi12.woohoo.in/rest/v3/catalog/products/%s"
    GetProductListBaseUrl: "https://extapi12.woohoo.in/rest/v3/catalog/categories/%s/products"
    # token is valid for 6 days since generation
    AccessTokenValidityDuration: "144h"
    MailOrderDetailsTo: "<EMAIL>"

  MoEngage:
    BaseUrl: "https://api-03.moengage.com/v1"

  Thriwe:
    BaseUrl: "https://india-api-gateway.thriwe.com"

  Riskcovry:
    BaseUrl: "https://api.riskcovry.com/api/partner"

  Onsurity:
    BaseUrl: "https://partner.onsurity.com"

  Nps:
    BaseUrl: "https://npscra.nsdl.co.in/download"

  Nugget:
    # adding non-prod values for now
    # update when we receive credentials
    BaseURL: "https://api.nugget.com"
    AccessTokenEndpoint: "/unified-support/auth/users/getAccessToken"
    BusinessId: 1

  Karvy:
    KarviAppId: "RIA"
    AgentCode: "INA200015185"
    BranchCode: "R999"
    BucketName: "epifi-prod-mutualfund-karvy"
    FATCAFileURL: "https://cpservices.kfintech.com/STP/STPService.svc/FATCAUpload"
    #OrderFeedFileSyncURL: "https://cpservices.kfintech.com/STP/STPService.svc/TransactionUpload168"  old end point
    OrderFeedFileSyncURL: "https://cpservices.kfintech.com/STP/STPService.svc/TransactionUpload200"
    # TODO(Rohit): remove V2 url after prod is moved to new url
    OrderFeedFileV2SyncURL: "https://cpservices.kfintech.com/STP/STPService.svc/TransactionUpload250"
    NFTFileUploadURL: "https://cpservices.kfintech.com/STP/STPService.svc/NCTUpload"
    GetFolioDetailsURL: "https://cpservices.kfintech.com/STP/STPService.svc/GetMobileAndEmailBasedOnFolio"
    NomineeUpdateURL: "https://cpservices.kfintech.com/NCTCF/NCTCFService.svc/NCTCFUpload"
    NCTCFTokenGenerateURL: "https://cpservices.kfintech.com/NCTCF/NCTCFService.svc/GenerateToken"
    UserCode: "EPFIRIA"

  #json file path
  PayFundTransferStatusCodeJson: "./mappingJson/fundTransferStatusCodes.json"
  PayUpiStatusCodeJson: "./mappingJson/upiStatusCodes.json"
  PayAckStatusCodeJson: "./mappingJson/ackStatusCode.json"

  DepositAckStatusCodeFilePath: "./mappingJson/depositAckStatusCode.json"
  DepositResponseStatusCodeFilePath: "./mappingJson/depositResponseStatusCodes.json"

  CardResponseStatusCodeFilePath: "./mappingJson/cardResponseStatusCodes.json"
  SIResponseStatusCodeFilePath: "./mappingJson/siResponseStatusCodes.json"

  Federal:
    CheckCustomerStatusForNonResidentURL: "https://gateway.federalbank.co.in/prod/prod/accountcreation/v1.0.0/enquiry"
    CreateCustomerForNonResidentURL: "https://gateway.federalbank.co.in/prod/prod/WebAccOpen/v1.0.0/custDataInsert"
    CustomerDetailsInsertURL: "https://gateway.federalbank.co.in/prod/prod/WebAccOpen/v1.0.0/custDataInsert"
    PanValidationV2Url: "https://gateway.federalbank.co.in/prod/prod/pan/v2.0.0/validate"
    PayIntraBankURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/intra/fundtransfer"
    PayNEFTURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/neft/fundtransfer"
    PayIMPSURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/imps/fundtransfer"
    PayRTGSURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/rtgs/fundtransfer"
    PayStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"
    PayIntraBankDepositURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/OwnDepositAccFT"
    RemitterDetailsFetchUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/fetchRemittanceDetails"
    RemitterDetailsV1FetchUrl: "https://gateway.federalbank.co.in/prod/prod/fundTransfer/v1.0.0/remitterFetch"
    BeneficiaryNameLookupUrl: "https://gateway.federalbank.co.in/prod/prod/account_utility/v1.0.0/BenefiAcctNameLookup"
    GetCsisStatusUrl: "https://gateway.federalbank.co.in/prod/prod/neobanking/v1.0.0/CSISStatusCheck"

    PayIntraBankCallbackURL: "https://vnotificationgw.epifi.in/openbanking/payment/federal"
    PayNEFTCallbackURL: "https://vnotificationgw.epifi.in/openbanking/payment/federal"
    PayIMPSCallbackURL: "https://vnotificationgw.epifi.in/openbanking/payment/federal"
    PayRTGSCallbackURL: "https://vnotificationgw.epifi.in/openbanking/payment/federal"

    // TODO(vivek): Confirm this url with others
    # B2C Payments
    PayB2CIntraBankURL: "https://gateway.federalbank.co.in:553/prod/prod/intrabank/fundtransfer"
    PayB2CIntraBankCallbackURL: "https://vnotificationgw.epifi.in/openbanking/payment/b2c/federal"
    PayB2CStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/transaction/enquiry"
    PayB2CBalanceURL: "https://gateway.federalbank.co.in:553/prod/prod/balance/enquiry"
    PayB2CImpsURL: "https://gateway.federalbank.co.in:553/prod/prod/imps/fundtransfer"
    PayB2CImpsCallbackURL: "https://vnotificationgw.epifi.in/openbanking/payment/b2c/federal"

    InquireOrReportGSTCollectionURL: "https://gateway.federalbank.co.in/prod/prod/digitalCredit/v1.0.0/figstadd"
    InquireOrReportGSTCollectionChannelId: "EPIFI"

    # Shipping Preference Service
    ShippingAddressUpdateURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/ShipAddrModification"
    ShippingAddressUpdateCallbackURL: "https://vnotificationgw.epifi.in/openbanking/shipping_preference/federal"

    # Card Service
    DebitCardCreateURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardCreation"
    DebitCardCreateCallbackURL: "https://vnotificationgw.epifi.in/openbanking/card/federal"
    DebitCardActivateURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardActivation"
    DebitCardEnquiryUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardEnqService"
    DebitCardPinSetUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/PinManagement"
    DebitCardPinChangeUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/PinManagement"
    DebitCardPinResetUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/PinManagement"
    DebitCardPinValidationUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/PinManagement"
    DebitCardBlockUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardBlock"
    DebitCardSuspendOnOffUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardControl"
    DebitCardLocationOnOffUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardControl"
    DebitCardECommerceOnOffUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardControl"
    DebitCardCVVEnquiryUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardCVVEnq"
    DebitCardLimitEnquiry: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardLimitEnq"
    DebitCardUpdateLimit: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardLimitUpdate"
    DebitCardDeliveryTracking: "https://gateway.federalbank.co.in:553/prod/prod/neobanking-card/v1.0.0/CardDeliveryTrack"
    DebitCardConsolidatedCardControlUrl: "https://gateway.federalbank.co.in/prod/prod/neobanking-card/v1.0.0/FHMCardONOFF"
    DebitCardPhysicalDispatchUrl: "https://gateway.federalbank.co.in/prod/prod/neobanking-card/v1.0.0/cardDispatch"
    DebitCardPhysicalDispatchCallbackUrl: "https://vnotificationgw.epifi.in/openbanking/cardPhysicalDispatch/federal"
    CheckDebitCardIssuanceFeeStatusUrl: "https://gateway.federalbank.co.in/prod/prod/digitalCredit/v1.0.0/GSTbifurcation"
    DebitCardCollectIssuanceFeeUrl: "https://gateway.federalbank.co.in/prod/prod/digitalCredit/v1.0.0/GSTbifurcation"

    # PAN Service
    PANValidationURL: "https://gateway.federalbank.co.in/prod/prod/pan/validation"
    PANAadhaarValidationURL: "https://gateway.federalbank.co.in:553/prod/prod/digitalCredit/v1.0.0/PANAadhaarValidate"

    EkycNameDobValidationURL: "https://gateway.federalbank.co.in/prod/prod/ekyc/namedob/validation"
    AadharMobileValidationURL: "https://gateway.federalbank.co.in/prod/prod/account_utility/v1.0.0/aadharMob"

    ShareDocWithVendorURL: "https://gateway.federalbank.co.in/prod/prod/digitalCredit/v1.0.0/documentSharing"

    # UN Name Check Service
    UNNameCheckURL: "https://gateway.federalbank.co.in/prod/prod/unofac/nameCheck"

    # Device Re-registration / Profile Update API
    DeviceReRegURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/device/re-registration"
    DeviceReRegCallbackUrl: "https://vnotificationgw.epifi.in/openbanking/auth/federal/user-device/re-register"

    # Device De-registration / Deactivate User
    DeviceDeRegURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/user-device/deactivation"

    # Generate OTP
    GenerateOTPURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/OTPGen"

    DeviceReactivationURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/user-device/reactivate"
    # Deposit service
    CreateFDURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/CreateFD"
    CreateSDURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/CreateSD"
    CreateRDURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/CreateRD"
    AutoRenewFdURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/fdRenewal"
    CloseDepositAccountURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/ClosingDepositAcc"
    GetDepositAccountDetailURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/GetAccDetails"
    GetPreClosureDetailURL: "https://gateway.federalbank.co.in/prod/prod/digitalCredit/v1.0.0/DepositAcctPreclosEnq"
    CheckDepositAccountStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/DepositEnq"
    DepositListAccountURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/GetAccList"
    InterestRateInfoURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/interestRateInfo"
    CalculateInterestDetailsURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/depositInterestCalculator"
    CreateDepositCallbackUrl: "https://vnotificationgw.epifi.in/openbanking/deposit/federal/create"
    PreCloseDepositCallbackUrl: "https://vnotificationgw.epifi.in/openbanking/deposit/federal/preclose"
    AutoRenewFdCallbackUrl: "https://vnotificationgw.epifi.in/openbanking/deposit/federal/AutoRenewFd"

    # Standing Instruction service
    CreateSIUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/recurringtransfer/standinginstruction"
    ExecuteSIUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/recurringpayment/execution"
    SICallbackUrl: "https://vnotificationgw.epifi.in/openbanking/payment/federal"
    ModifySIUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/recurringpayment/modification"
    RevokeSIUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/recurringpayment/revoke"

    # csv file path
    CityCodesCsv: "./mappingCsv/cityCodes.csv"
    StateCodesCsv: "./mappingCsv/stateCodes.csv"
    CountryCodesCsv: "./mappingCsv/countryCodes.csv"

    #Account
    OpeningBalanceURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/GetAccStatement"
    ClosingBalanceURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/eodbalanceInquiry"
    AccountStatementURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/GetAccStatement"
    AccountStatementByDRApiUrl: "https://gateway.federalbank.co.in/prod/prod/neobanking/v1.0.1/getAccountStatement"
    EnquireBalanceV1URL: "https://gateway.federalbank.co.in:443/prod/prod/account_utility/v1.0.0/getGAMNotification"
    MiniStatementURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/ministatement"
    AccountStatusURL: "https://gateway.federalbank.co.in/prod/prod/account_utility/v1.0.0/accountStatusEnquiry"
    ThirdPartyAccountCollectionURL: "https://gateway.federalbank.co.in/prod/prod/account_utility/v1.0.0/tpAccountCollection"
    UpdateNomineeURL: "https://gateway.federalbank.co.in/prod/prod/account_utility/v1.0.0/nominationUpdate"
    UpdateNomineeChannel: "EPIFI"

    # Partner SDK
    GetSessionParamsUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/DeviceKeyReg"

    # Enquiry Service Urls
    CustomerCreationEnquiryStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"
    AccountCreationEnquiryStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"
    CardCreationEnquiryStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"
    DeviceReRegistrationDetailsEnquiryStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"
    MailingAddressModifyDetailsEnquiryStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"
    ShippingAddressUpdateEnquiryStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"
    DeviceRegistrationDetailsEnquiryStatusURL: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"
    PhysicalCardDispatchDetailsEnquiryStatus: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service"

    # Chequebook Request and Track URLs
    OrderChequebookUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/chequeBookRequest"
    TrackChequebookUrl: "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/chequeBookTrack"
    IssueDigitalCancelledChequeUrl: "https://gateway.federalbank.co.in/prod/prod/account_utility/v1.0.0/digitalChequeLeafIssuance"

    TcsCalculationURL: "https://gateway.federalbank.co.in/prod/prod/digitalCredit/v1.0.0"
    TcsCalculationChannelId: "EPI"

    # Profile Update and enquiry URL
    ProfileUpdateUrl: "https://gateway.federalbank.co.in/prod/prod/digitalCredit/v1.0.0/profileUpdation"
    ProfileUpdateEnquiryUrl: "https://gateway.federalbank.co.in/prod/prod/digitalCredit/v1.0.0/profileUpdation"

    # e-nach service url
    ListEnachUrl: "https://gateway.federalbank.co.in/prod/prod/NACH/EMANDATE/v1.0.0/dataSharing"

    FetchEnachTransactionsUrl: "https://gateway.federalbank.co.in/prod/prod/digitalCredit/v1.0.0/enachdata"

  LeadSquared:
    CreateOrUpdateLeadUrl: "https://api-in21.leadsquared.com/v2/LeadManagement.svc/Lead.CreateOrUpdate?postUpdatedLead=false&accessKey=%s&secretKey=%s"

  Karza:
    GenerateSessionTokenUrl: "https://api.karza.in/v3/get-jwt"
    AddNewCustomerUrl: "https://app.karza.in/prod/videokyc/api/v2/customers"
    AddNewCustomerV3Url: "https://app.karza.in/prod/videokyc/api/v3/customers"
    UpdateCustomerV3Url: "https://app.karza.in/prod/videokyc/api/v3/customers"
    GenerateCustomerTokenUrl: "https://app.karza.in/prod/videokyc/api/v2/generate-usertoken"
    GetSlotUrl: "https://app.karza.in/prod/videokyc/api/v2/get-slot"
    BookSlotUrl: "https://app.karza.in/prod/videokyc/api/v2/book-slot"
    GenerateWebLinkUrl: "https://app.karza.in/prod/videokyc/api/v2/link"
    SlotAgentsUrl: "https://app.karza.in/prod/videokyc/api/v2/slot-agents"
    TransactionStatusEnquiryUrl: "https://app.karza.in/prod/videokyc/api/v2/transaction-events"
    ReScheduleSlotUrl: "https://app.karza.in/prod/videokyc/api/v2/reschedule-customer"
    TriggerCallback: "https://app.karza.in/prod/videokyc/api/v2/trigger-callback"
    AgentDashboardUrl: "https://app.karza.in/prod/videokyc/api/v2/stats/agents"
    AgentDashboardAuthUrl: "https://app.karza.in/prod/videokyc/api/v3/login"
    EmploymentVerificationAdvancedUrl: "https://api.karza.in/v2/employment-verification-advanced"
    KycOcrUrl: "https://api.karza.in/v3/ocr-plus/kyc"
    PassportVerificationURL: "https://api.karza.in/v3/passport-verification"

  Roanuz:
    GenerateCricketAccessTokenUrl: "https://api.sports.roanuz.com/v5/core"
    CricketURL: "https://api.sports.roanuz.com/v5/cricket"
    GenerateFootballAccessTokenUrl: "https://api.footballapi.com/v1/auth"
    FootballUrl: "https://api.footballapi.com/v1"
    FootballAppId: "com.epififootball.www"
    FootballDeviceId: "developer"

  IPStack:
    GetLocationDetailFromIpUrl: "https://api.ipstack.com"

  CvlKra:
    SoapHost: "https://pancheck.www.kracvl.com"
    PanEnquiryURL: "https://pancheck.www.kracvl.com/CVLPANInquiry.svc"
    InsertUpdateKycURL: "https://pancheck.www.kracvl.com/CVLPanInquiry.svc"
    # CvlKra sftp config
    Host: "dm.cvlindia.com"
    Port: 4443

  NsdlKra:
    PanInquiryURL: "https://opvapi.egov.proteantech.in/TIN/PanInquiryBackEnd"
    GenerateSignatureURL: "https://prod-nsdl-forwardsecrecy.epifi.in/nsdl/v1/generateSignature"
    PerformPanInquiryV4URL: "https://opvapi.egov.proteantech.in/TIN/PanInquiryAPIBackEnd"

  Ckyc:
    SearchURL: "https://www.ckycindia.in/Search/ckycverificationservice/verify"
    DownloadURL: "https://www.ckycindia.in/Search/ckycverificationservice/download"
    ApiVersion: "1.2"
    EnableCryptor: true

  Manch:
    TransactionsURL: "https://manchtech.com/app/api/transactions"
    DocumentsURL: "https://manchtech.com/app/api/documents"
    OrgId: "ORG00064"
    ReturnUrl: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"

  Digio:
    TransactionsURL: "https://dummy-endpoint/v2/client/document"
    ExpiryInDays: 10

  WealthKarza:
    OcrURL: "https://dummy-endpoint/v3/kycocr"
    MaskAadhaar: true
    HideAadhaar: true
    Confidence: true
    CheckBlur: true
    CheckBlackAndWhite: true
    CheckCutCard: true
    CheckBrightness: true

  Experian:
    CheckCreditReportPresenceURL: "https://consumer.experian.in:8443/ECV-P2/content/enhancedMatch.action"
    FetchCreditReportURL: "https://consumer.experian.in:8443/ECV-P2/content/enhancedMatch.action"
    FetchCreditReportForExistingUserURL: "https://consumer.experian.in:8443/ECV-P2/content/onDemandRefresh.action"
    FetchExtendSubscriptionURL: "https://consumer.experian.in:8443/ECV-P2/content/consumerConsentReRegistration.action"
    FetchCreditReportURLV1: "https://in-api.experian.com/ecs/ecv-api/v1/enhanced-match"
    FetchCreditReportForExistingUserURLV1: "https://in-api.experian.com/ecs/ecv-api/v1/ondemand-refresh"
    FetchExtendSubscriptionURLV1: "https://in-api.experian.com/ecs/ecv-api/v1/consumer-consent-reregistration"
    FetchAccessTokenUrl: "https://in-api.experian.com/oauth2/v1/token"
    V1VersionFlag: true

  Cibil:
    PingUrl: "https://api.transunioncibil.com/consumer/dtc/v4/ping"
    FulfillOfferUrl: "https://api.transunioncibil.com/consumer/dtc/v4/fulfilloffer"
    GetAuthQuestionsUrl: "https://api.transunioncibil.com/consumer/dtc/v4/GetAuthenticationQuestions"
    VerifyAuthAnswersUrl: "https://api.transunioncibil.com/consumer/dtc/v4/VerifyAuthenticationAnswers"
    GetCustomerAssetsUrl: "https://api.transunioncibil.com/consumer/dtc/v4/GetCustomerAssets"
    GetProductTokenUrl: "https://api.transunioncibil.com/consumer/dtc/v4/GetProductWebToken"
    ProductUrlPrefix: "https://myscore.cibil.com/CreditView"
    DisableDefaultValuesInFulfillOffer: false

  Shipway:
    BulkUploadShipmentDataUrl: "https://shipway.in/api/pushOrders"
    GetShipmentDetailsUrl: "https://shipway.in/api/getOrderShipmentDetails"
    AddOrUpdateWebhookUrl: "https://shipway.in/api/delete_webhooks"
    UploadShipmentDataUrl: "https://shipway.in/api/PushOrderData"

  # AA service vendor URLs
  AA:
    BaseURL: "" #Fetched from Central registry
    PostConsentURL: "/Consent"
    ConsentStatusURL: "/Consent/handle"
    ConsentArtefactURL: "/Consent"
    RequestDataURL: "/FI/request"
    FetchDataURL: "/FI/fetch"
    GetAccountLinkStatusURL: "/Account/link/status"
    GenerateAccessTokenURL: "https://api.sahamati.org.in/iam/v1/entity/token/generate"
    FetchCrEntityDetailURL: "https://cr.sahamati.org.in/entityInfo/AA"
    FetchCrEntityDetailURLV2: "https://cr.sahamati.org.in/v2/entityInfo/AA"
    ConsentUpdateURL: "/consent/update"
    AccountDeLinkURL: "/account/delink"
    UseSahamatiCrAndToken: true
    OneMoneyCrId: "onemoney"
    FinvuCrId: "cookiejaraalive@finvu"
    GetAccountLinkStatusBulkURL: "/Account/link/Status"
    GetHeartbeatStatusURL: "/Heartbeat"
    EpifiAaKid: "38283ca6-0463-4dde-9f77-cce2c270c99d"
    SahamatiClientId: "EPIFIPROD"
    GenerateFinvuJwtTokenURL: "/web/token"
    GetBulkConsentRequestURL: "/Consent/status/bulk"
    AaSecretsVersionToUse: "V1"
    FinvuFipMetricsURL: "/fip/latest-metrics-all"
    IsOnemoneyV2Enabled: true
    IsFinvuV2Enabled: true
    Ignosis:
      Url: "https://ignosis.epifi.in:8086/api/v1/fimoney"

  # Bouncy castle library URLs
  BouncyCastle:
    GenerateKeyPairURL: "https://prod-bouncycastle.epifi.in/ecc/v1/generateKey"
    GetSharedSecretURL: "https://prod-bouncycastle.epifi.in/ecc/v1/getSharedKey"
    DecryptDataURL: "https://prod-bouncycastle.epifi.in/ecc/v1/decrypt"
    CkycEncryptAndSignURL: "https://prod-nsdl-forwardsecrecy.epifi.in/ckyc/v1/encryptAndSign"
    CkycVerifyAndDecryptURL: "https://prod-nsdl-forwardsecrecy.epifi.in/ckyc/v1/verifyAndDecrypt"

  # Fennel Vendor URLs
  FennelFeatureStore:
    ExtractFeatureSetsURL: "https://main.epifi.aws.fennel.ai/api/v1/extract_features"
    ExtractFeatureSetsURLV2: "https://epifi-prod.aws.fennel.ai/api/v1/query"
    ExtractFeatureSetsURLV3: "https://babel.data-prod.epifi.in/api/v1/query"
    LogDatasetsURL: "https://main.epifi.aws.fennel.ai/api/v1/log"
    LogDatasetsURLV2: "https://epifi-prod.aws.fennel.ai/api/v1/log"
    LogDatasetsURLV3: "https://babel.data-prod.epifi.in/api/v1/log"
    PdScoreURL: "https://loan-default.data-prod.epifi.in/v2/loan_default"
    Simulator:
      Enable: false

  # Scienaptic Vendor Config
  Scienaptic:
    GenerateSmsFeaturesURL: "https://query.sms-scienaptic.epifi.in/scraper/generate_feature"

  InhouseOCR:
    MaskDocURL: "https://ocular.data-prod.epifi.in/v1/mask_doc"
    ExtractFieldsURL: "https://ocular.data-prod.epifi.in/v1/extract_fields"
    DetectDocumentURL: "https://ocular.data-prod.epifi.in/v1/detect_doc"
    ExtractFieldsURLV2: "https://ocular.data-prod.epifi.in/v1/extract_fields"

  InhousePopularFAQUrl: "https://popular-faqs.data-prod.epifi.in"

  Digilocker:
    GetAuthorizationCodeUrl: "https://api.digitallocker.gov.in/public/oauth2/1/authorize"
    GetAccessTokenUrl: "https://api.digitallocker.gov.in/public/oauth2/1/token"
    ClientId: "EBB0DE86"
    RedirectUri: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"
    GetListOfIssuedDocumentsUrl: "https://api.digitallocker.gov.in/public/oauth2/2/files/issued"
    GetRefreshTokenUrl: "https://api.digitallocker.gov.in/public/oauth2/1/token"
    GetFileFromUriUrl: "https://api.digitallocker.gov.in/public/oauth2/1/file/"
    GetAadhaarInXmlUrl: "https://api.digitallocker.gov.in/public/oauth2/3/xml/eaadhaar"
    TokenCacheTtl: 720h # 30 days

  Liquiloans:
    Host: "https://supply-integration.liquiloans.com/api"
    SupplyIntegrationHost: "https://supply-integration.liquiloans.com/api"
    SftpHost: sftp.liquiloans.com
    SftpPort: 22
    S3BucketP2PInvestmentLedger: "epifi-prod-p2p-investor-ledger"
    SftpTimeoutInSeconds: 180

  Lending:
    PreApprovedLoan:
      Federal:
        UrlLentra: "https://gateway.federalbank.co.in/prod/prod/digitalCredit/v1.0.0/lentraBreWrapper"
        Url: "https://gateway.federalbank.co.in/prod/prod"
        HttpUrl: "https://gateway.federalbank.co.in/prod/prod"
        FetchDetailsUrl: "https://gateway.federalbank.co.in/prod/prod/account_utility/v1.0.0/fetchLoanDetails"
        RespUrl: ""
        # TODO(@kantikumar): update sftp host once available
        SftpHost: ""
        SftpPort: 22
        PlAcntCrnNtbHttpURL: "https://gateway.federalbank.co.in/prod/prod/loan/account/v2.0.0/creation"
        PlAcntCrnEnqNtbHttpURL: "https://gateway.federalbank.co.in/prod/prod/loan/v1.0.0/enquiry"
      # TODO(@Shivansh) update URL once available
      Liquiloans:
        Url: "https://api.liquiloans.com"
        # This URL points to HTTP port of simulator as few Liquiloans API needs request body  in HTTP GET request, those are
        # mocked using Router and not by gRPC method.
        HttpUrl: "https://api.liquiloans.com"
      Idfc:
        # Old endpoint
        # Url: "https://apiext.idfcfirstbank.com"
        # New endpoint after IDFC's internal migration
        Url: "https://ent.api.idfcfirstbank.com"
        # URL to fetch the access token for IDFC APIs
        GetAccessTokenUrl: "https://app.my.idfcfirstbank.com/platform/oauth/oauth2/token"
        MandatePageUrl: "https://partner.idfcfirstbank.com/IDFCEMandate/EMandateB2BPaynimmo.aspx"
        EnableEncryption: true
        Source: "FIMONEY"
      Abfl:
        Url: "https://b2bpartner-api.abfldirect.com"
        BreUrl: "https://b2bpartner-api.abfldirect.com/v2/decisionEngine"
        TxnDetailsUrl: "https://b2bpartner-api.abfldirect.com"
        PwaJourneyUrl: "https://lendingapis.finbox.in"
      Moneyview:
        BaseUrl: "https://atlas.whizdm.com/atlas/v1"
        # this http url is used for differentiating http and hrpc call for simulator, but in prod we are using the same url
        HttpUrl: "https://atlas.whizdm.com/atlas/v1"
      Setu:
        BaseUrl: "https://federal-solutions.setu.co"
      Digitap:
        UanAdvancedUrl: "https://svc.digitap.ai/cv/v3/uan_advanced/sync"
      Lenden:
        BaseUrl: "https://tsp-gateway.lendenclub.com/v1/EPF/"
        ProductId: "EcoX-Loan-102"
        EnableCryptor: true
      Finflux:
        BaseUrl: "https://finfluxprod.lms.epifi.in"
        Auth:
          IsPasswordEncrypted: false
          TokenValidityDuration: "14m"
        Charges:
          ProcessingFeeChargeId: 1
        #  This is a map against loanStatusVendorId and Value is the stringified version of api.vendorgateway.lending.lms.finflux.types.LoanStatus
        LoanStatusVendorIdToEnumValue:
          100: "LOAN_STATUS_SUBMITTED_AND_AWAITING_APPROVAL"
          200: "LOAN_STATUS_APPROVED"
          300: "LOAN_STATUS_ACTIVE"
          303: "LOAN_STATUS_TRANSFER_IN_PROGRESS"
          304: "LOAN_STATUS_TRANSFER_ON_HOLD"
          400: "LOAN_STATUS_WITHDRAWN_BY_CLIENT"
          500: "LOAN_STATUS_REJECTED"
          600: "LOAN_STATUS_CLOSED"
          601: "LOAN_STATUS_WRITTEN_OFF"
          602: "LOAN_STATUS_RESCHEDULED"
          700: "LOAN_STATUS_OVERPAID"

    CreditCard:
      M2P:
        RegisterCustomerHost: "https://federalapi.m2pfintech.com/"
        M2PHost: "https://federalapi.m2pfintech.com/"
        CreditCardRepaymentHost: "https://federalapi.m2pfintech.com/"
        M2PFallbackHost: "https://federalapi.m2pfintech.com/"
        M2PLMSHost: "https://federalapi.m2pfintech.com/"
        M2PPartnerSdkUrl: "https://federalapi.m2pfintech.com/gateway/"
        M2PSetPinUrl: "https://federalapi.m2pfintech.com/gateway/"
        EnableEncryption: true
        M2PFederalHost: "https://federalapi.m2pfintech.com/"
        # Property to rotate the RSA key to the newly created key.
        RotateKey: false
      Federal:
        Url: "https://gateway.federalbank.co.in/prod/prod/CreditCard/v1.0.0/"
        UpdateCreditCardLimitDetailsUnsecuredChannel: "M2P-FDEPIFICR"
        UpdateCreditCardLimitDetailsMassUnsecuredChannel: "M2P-FDEPIFIMASSCR"
    CreditLine:
      Federal:
        Url: "https://gateway.federalbank.co.in/prod/prod/CreditCard/v1.0.0/limitFetch"
      M2P:
        Url: "https://federalapi.m2pfintech.com/api/v1/customer"
    Collateral:
      LendingMFCentralConfig:
        HttpUrl:
          AuthToken: "https://mfcentral/oauth/token"
          EncryptAndSign: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/encryptAndSign"
          DecryptAndVerify: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/verifyAndDecrypt"
          SubmitCasSummary: "https:/mfcentral/submitcassummary"
          InvestorConsent: "https://mfcentral/investorconsent"
          GetCasDocument: "https://mfcentral/getcasdocument"
          ValidateLien: "https://mfcentral/validatelien"
          SubmitLien: "https://mfcentral/submitlien"
          InvokeRevokeLien: "https://mfcentral/validateLienInvokeRevoke"
          CheckStatus: "https://mfcentral/lienCheckStatus"
          GetTransactionStatus: "https://mfcentral/getTransactionStatus"
    SecuredLoans:
      Url: "https://external-api.50fin.in"

  Alpaca:
    BrokerApiHost: "https://broker-api.alpaca.markets"
    BrokerApiVersion: "v1"
    MarketApiHost: "https://data.alpaca.markets"
    MarketApiVersion: "v2"
    StreamApiHost: "stream.data.alpaca.markets"
    StreamApiPath: "v2/iex"
    StreamApiScheme: "wss"
    ShouldUseSimulatedEnvForWs: false
    OrderEventsApiPath: "/v1/events/trades"
    AccountEventsApiPath: "/v1/events/accounts/status"
    FundTransferEventsPath: "/v1/events/transfers/status"
    JournalEventsPath: "/v1/events/journals/status"
    BrokerEventsApiHost: "broker-api.alpaca.markets"
    BrokerEventsApiScheme: "https"
    ShouldUseSimulatedEnvForEvents: false
    MarketDataBetaAPIPrefix: "v1beta1"

  MorningStar:
    TokenCacheTtl: 12h
    EquityAPIURL: "https://equityapi.morningstar.com"
    ApiUrl: "https://api.morningstar.com"
    ApiAccessTokenExpiryInDays: 90
    MorningStarObsoleteFundAPIUrl: "https://intools.morningstar.com/identifier/api/data"

  FederalInternationalFundTransfer:
    URL: "https://gateway.federalbank.co.in/prod/prod/LRS/v1.0.0"
    CheckLRSEligibilityPrecision: 9

  Esign:
    Leegality:
      Url: "https://app1.leegality.com/api/v3.0/"

  ProfileValidation:
    Federal:
      Url: "https://gateway.federalbank.co.in/prod/prod/"

  #GPlace Vendor URLs
  GPlace:
    FindPlaceReqURL: "https://maps.googleapis.com/maps/api/place/findplacefromtext/json"
    GetPlaceDetailsReqURL: "https://maps.googleapis.com/maps/api/place/details/json"

  GoogleReverseGeocodingUrl: "https://maps.googleapis.com/maps/api/geocode/json"
  GoogleGeocodingUrl: "https://maps.googleapis.com/maps/api/geocode/json"
  InhouseLocationServiceUrl: "https://geo.data-prod.epifi.in"
  MaxmindIp2CityUrlPrefix: "https://geoip.maxmind.com/geoip/v2.1/city/"

  PAYUAffluenceURL: "https://mars.payu.in/api/v3/daas/"

  BureauPhoneNumberDetailsUrl: "https://api.overwatch.bureau.id/v1/suppliers/phone-network"

  #DronaPay
  DronapayHostURL: "https://risk.epifi.in/springapi"

  InhouseRiskServiceURL: "https://onboarding-risk-detection.data-prod.epifi.in/v3/onboarding_risk_detection"
  InhouseRiskServiceURLV1: "https://onboarding-risk-detection.data-prod.epifi.in/v1/shadow_onboarding_risk_detection"
  InhouseReonboardingRiskServiceURL: "https://onboarding-risk-detection.data-prod.epifi.in/v1/afu_risk_detection"
  CasePrioritisationModel:
    InHouseUrl: "https://ds-wise-prioritise.data-prod.epifi.in/prioritisation/v0"

  "InhouseMerchantResolutionServiceUrl": "https://merchant.data-prod.epifi.in/resolution"

  Aml:
    Tss:
      Epifi:
        ScreeningUrl: "https://aml-lookup.epifi.in/crmapi/a44twcustomerscreeningalertcreationcontroller/GenerateScreeningAlert"
        SystemName: "Epifi_tech"
        ParentCompany: "Epifi"
      StockGuardian:
        ScreeningUrl: "https://aml-lookup.epifi.in/crmapi/a44twcustomerscreeningalertcreationcontroller/GenerateScreeningAlert"
        SystemName: "LMS"
        ParentCompany: "SGIPL"

  LocationModel:
    InHouseUrl: "https://onboarding-risk-detection.data-prod.epifi.in/v1/geo_score"
    RiskSeverityValToEnumMapping:
      "LOW": "RISK_SEVERITY_LOW"
      "MEDIUM": "RISK_SEVERITY_MEDIUM"
      "HIGH": "RISK_SEVERITY_HIGH"
      "CRITICAL": "RISK_SEVERITY_CRITICAL"

  Credgenics:
    BaseUrl: "https://apiprod.credgenics.com/recovery"

  IncomeEstimatorConf:
    InhouseIncomeEstimatorURL: "https://fortuna-ds.data-prod.epifi.in/income_fetch"

  EpanConfig:
    FederalConfig:
      SftpConn:
        Host: "sfg.federalbank.co.in"
        Port: 10022

  EnachConfig:
    FederalConfig:
      SftpConn:
        Host: "sfg.federalbank.co.in"
        Port: 10022
      FederalSFTPUploadPath: "/TRANSACTIONS/IN/"
      FederalSFTPDownloadPath: "/TRANSACTIONS/OUT/"
      FileTypeToSFTPPathMap:
        FILE_TYPE_PRESENTATION_FILE: "/TRANSACTIONS/IN/"
        FILE_TYPE_PRESENTATION_ACK_FILE: "/TRANSACTIONS/OUT/"
        FILE_TYPE_PRESENTATION_RESPONSE_FILE: "/TRANSACTIONS/OUT/"

  Dreamfolks:
    BaseUrl: "https://v4.dreamfolks.in"
    ProgramId: "**********"
    ServiceId: "11"

  Poshvine:
    BaseUrl: "https://api.poshvine.com"
    SSOUrl: "/cs/v1/sso/token_login"
    UpdateUrl: "/cs/external/users/update"
    FiClientId: "efa07a16-273f-4fd0-9866-c1d42c5d0eac"
    ExpiresAt: "24h"
    RedirectionUrl: "https://fimoney.poshvine.com/sso_login"

  NetCoreEpifi:
    URL: "https://bulkpush.mytoday.com/BulkSms/SingleMsgApi"
    FeedId: "393955"

  Visa:
    FindNearbyAtmTotalsUrl: "https://api.visa.com/globalatmlocator/v3/localatms/totalsinquiry"
    FindNearbyAtmsUrl: "https://api.visa.com/globalatmlocator/v3/localatms/atmsinquiry"
    FindGeocodesUrl: "https://api.visa.com/globalatmlocator/v3/localatms/geocodesinquiry"
    GetEnhancedForeignExchangeRatesUrl: "https://api.visa.com/fx/rates"
    GetEnhancedMarkupForeignExchangeRatesUrl: "https://api.visa.com/fx/rates/markup"

  Saven:
    CreditCardBaseUrl: "https://federal-fi.m2pfintech.com"
    JwtExpiry: "3s"

  SetU:
    PartnerId: "1510324753200579781"
    BaseURL: "https://sandbox-coudc.setu.co" # todo[obed]: update to prod url
    MobileRechargeProductInstanceId: "fa36b3f6-78e5-4251-b988-abf9640fd0b3"
    MobileRechargeLoginBaseUrl: "https://accountservice.setu.co"
    MobileRechargeBaseUrl: "https://prepaid.setu.co"

  PerfiosDigilocker:
    ApiHost: "https://api-in-uat.perfios.com/kyc/api/v1/digilocker"

  FederalEscalation:
    BaseURL: "https://gateway.federalbank.co.in"
    CreateEscalationURL: "prod/prod/CRM/v1.0.0/cxiosrcreation"
    BulkFetchURL: "prod/prod/CRM/v1.0.0/cxsrbulkretrieval"
    S3BucketName: "epifi-prod-cx-ticket-attachments"

  Bridgewise:
    ApiHost: "https://rest.bridgewise.com"
    TokenCacheTtl: 12h

Server:
  Ports:
    GrpcPort: 8081
    GrpcSecurePort: 9522
    HttpPort: 9999

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    #M2P
    M2PSecrets: "prod/vendorgateway/m2p"
    M2PSecuredCardSecrets: "prod/vendorgateway/m2p/secured"
    # TODO(priyansh) : Get this secret added
    M2PMassUnsecuredCardSecrets: "prod/vendorgateway/m2p/mass-unsecured"
    EpifiM2pRsaPrivateKey: "prod/vendorgateway/epifi-m2p-private-key"
    EpifiM2pRsaPrivateKeyV2: "prod/vg-vgpci/epifi-m2p-private-key-2023"
    EpifiM2pRsaPublicKey: "prod/vendorgateway/m2p-public-key"

    # In-house BRE
    InHouseBreBearer: "prod/vendorgateway/inhouse-bre-bearer"

    #Federal
    EpifiFederalPgpPrivateKey: "prod/pgp/pgp-epifi-fed-api-private-key"
    EpifiFederalPgpPassphrase: "prod/pgp/pgp-epifi-fed-api-password"
    FederalPgpPublicKey: "prod/pgp/federal-pgp-pub-key-for-epifi"
    EpifiFederalUPIPrivateKey: "prod/vendorgateway/upi-xml-signature-v1-2024"
    EpifiFederalUPIFallbackPrivateKey: "prod/vendorgateway/upi-xml-signature-v1-2023"
    SenderCode: "prod/vg-vn-simulator-vgpci/federal-auth-sender-code"
    ServiceAccessId: "prod/vg-vn-vgpci/federal-auth-service-access-id"
    ServiceAccessCode: "prod/vg-vn-vgpci/federal-auth-service-access-code"
    ClientId: "prod/vg-vgpci/federal-auth-client-id"
    ClientSecretKey: "prod/vg-vgpci/federal-auth-client-secret-key"
    EpifiFederalCardDataPrivateKeyFallBack: "prod/vg-vngw-vgpci/rsa-federal-card-data"
    EpifiFederalCardDataPrivateKey: "prod/vg-vngw-vgpci/federal-card-data-decryption"

    RemittanceFederalIntermediateCA: "prod/vendorgateway/federal-remittance-intermediate-ca"
    RemittanceFederalIntermediateCAV1: "prod/vendorgateway/v1/federal-remittance-intermediate-ca"
    RemittanceFederalIntermediateCAV2: "prod/vendorgateway/v2/federal-remittance-intermediate-ca"
    RemittanceFederalRootCA: "prod/vendorgateway/federal-remittance-root-ca"
    RemittanceFederalRootCAV1: "prod/vendorgateway/v1/federal-remittance-root-ca"
    RemittanceFederalRootCAV2: "prod/vendorgateway/v2/federal-remittance-root-ca"

    #Closing Balance params
    ClosingBalanceCredentials: "prod/vendorgateway/federal-closing-balance-secrets"

    GetBalanceCredentialsV1: "prod/vendorgateway/federal-get-balance-v1-secrets"

    GetRemitterDetailsCredentials: "prod/vendorgateway/federal-get-remitter-details-secrets"
    GetRemitterDetailsV1Credentials: "prod/vendorgateway/federal-get-remitter-details-secrets-v1"
    GetBeneficiaryNameDetailsCredentials: "prod/vendorgateway/federal-get-beneficiary-name-details-secrets"
    GetCsisStatusCredentials: "prod/vendorgateway/federal-get-csis-status-secrets"

    #FCM
    FCMServiceAccountCredJson: "prod/vendorgateway/fcm-account-credentials"
    #Sendgrid
    SendGridAPIKey: "prod/vendorgateway/sendgrid-api-key"
    #TLS certs
    SimulatorCert: "prod/vg-vgpci/tls-client-cert-for-sim"
    EpiFiFederalClientSslCert: "prod/vg-vgpci/tls-client-cert-for-federal-2024"
    EpiFiFederalClientSslKey: "prod/vg-vgpci/tls-client-priv-key-for-federal-2024"
    #Freshdesk
    FreshdeskApiKey: "prod/vendorgateway/freshdesk-api-key"
    EpifiTechRiskFreshdeskApiKey: "prod/vendorgateway/epifitech-risk-freshdesk-api-key"
    #Ozonetel
    OzonetelApiKey: "prod/vendorgateway/ozonetel-api-key"
    #Freshchat
    FreshchatApiKey: "prod/vendorgateway/freshchat-api-key"
    # Pan Validation
    PanValidationAccessId: "prod/vendorgateway/federal-auth-pan-validation-access-id"
    PanValidationAccessCode: "prod/vendorgateway/federal-auth-pan-validation-access-code"
    #Loylty
    LoyltyClientId: "prod/vendorgateway/loylty-auth-client-id"
    LoyltyClientKey: "prod/vendorgateway/loylty-auth-client-key"
    LoyltyClientSecret: "prod/vendorgateway/loylty-auth-client-secret"
    LoyltyClientEncryptionKey: "prod/vendorgateway/loylty-auth-client-encryption-key"
    LoyltyEGVModuleId: "prod/vendorgateway/loylty-auth-egv-module-id"
    LoyltyCharityModuleId: "prod/vendorgateway/loylty-auth-charity-module-id"
    LoyltyApplicationId: "prod/vendorgateway/loylty-auth-application-id"
    LoyltyProgramId: "prod/vendorgateway/loylty-auth-program-id"

    # Qwikcilver
    QwikcilverSecrets: "prod/vendorgateway/qwikcilver-secrets"

    #Thriwe
    ThriweSecrets: "prod/vendorgateway/thriwe-secrets"

    #Riskcovry
    RiskcovrySecrets: "prod/vendorgateway/riskcovry-secrets"

    #Onsurity Secrets
    OnsuritySecrets: "prod/vendorgateway/onsurity-secrets"

    # UPI API
    UPISenderUserId: "prod/vendorgateway/federal-upi-sender-user-id"
    UPISenderPassword: "prod/vendorgateway/federal-upi-sender-password"
    UPISenderCode: "prod/vendorgateway/federal-upi-sender-code"
    UPISenderUserIdfbl: "prod/vendorgateway/federal-upi-sender-user-id"
    UPISenderPasswordfbl: "prod/vendorgateway/federal-upi-sender-password"
    UPISenderCodefbl: "prod/vendorgateway/federal-upi-sender-code"
    UPISenderUserIdfifederal: "prod/vendorgateway/federal-upi-ffi-sender-user-id"
    UPISenderPasswordfifederal: "prod/vendorgateway/federal-upi-ffi-sender-password"
    UPISenderCodefifederal: "prod/vendorgateway/federal-upi-ffi-sender-code"
    UPISenderUserIdIosAddFunds: "prod/vendorgateway/federal-upi-sender-user-id-ios-add-funds"
    UPISenderPasswordIosAddFunds: "prod/vendorgateway/federal-upi-sender-password-ios-add-funds"
    UPISenderCodeIosAddFunds: "prod/vendorgateway/federal-upi-sender-code-ios-add-funds"

    # SMS API keys
    TwilioAccountSid: "prod/vendorgateway/twilio-account-sid"
    TwilioApiKey: "prod/vendorgateway/twilio-api-key"
    ExotelApiKey: "prod/vendorgateway/exotel-api-key"
    ExotelApiToken: "prod/vendorgateway/exotel-api-token"
    AclEpifiUserId: "prod/vendorgateway/acl-epifi-user-id"
    AclEpifiPassword: "prod/vendorgateway/acl-epifi-password"
    AclFederalUserId: "prod/vendorgateway/acl-federal-user-id"
    AclFederalPassword: "prod/vendorgateway/acl-federal-password"
    AclEpifiOtpUserId: "prod/vendorgateway/acl-epifi-otp-user-id"
    AclEpifiOtpPassword: "prod/vendorgateway/acl-epifi-otp-password"
    KaleyraFederalApiKey: "prod/vendorgateway/kaleyra-federal-api-key"
    KaleyraEpifiApiKey: "prod/vendorgateway/kaleyra-epifi-api-key"
    KaleyraFederalCreditCardApiKey: "prod/vendorgateway/kaleyra-federal-cc-api-key"
    KaleyraEpifiNRApiKey: "prod/vendorgateway/kaleyra-epifi-nr-api-key"
    AclWhatsappUserId: "prod/vendorgateway/acl-whatsapp-user-id"
    AclWhatsappPassword: "prod/vendorgateway/acl-whatsapp-password"
    WhatsappEnterpriseId: "prod/vendorgateway/whatsapp-enterprise-id"
    WhatsappEnterpriseToken: "prod/vendorgateway/whatsapp-enterprise-token"
    # Video kyc KARZA api key
    KarzaVkycApiKey: "prod/vendorgateway/karza-vkyc-apikey"
    KarzaVkycPriorityApiKey: "prod/vendorgateway/karza-vkyc-priority-apikey"
    KarzaReVkycPriorityApiKey: "prod/vendorgateway/karza-re-vkyc-priority-apikey"
    KarzaSecrets: "prod/vendorgateway/karza"

    # Rounza cricket api's project and api key
    RounazCricketProjectKey: "prod/vendorgateway/roanuz-project-key"
    RounazCricketApiKey: "prod/vendorgateway/roanuz-api-key"

    # Rounaz football api's access and secret key
    RounazFootballAccessKey: "prod/vendorgateway/roanuz-football-access-key"
    RounazFootballSecretKey: "prod/vendorgateway/roanuz-football-secret-key"

    # B2C payments keys
    B2cUserId: "prod/vendorgateway/federal-auth-b2c-payment-user-id"
    B2cPassword: "prod/vendorgateway/federal-auth-b2c-payment-password"
    B2cSenderCodeKey: "prod/vendorgateway/federal-auth-b2c-payment-sender-code"

    # ipstack access key
    IpstackAccessKey: "prod/vendorgateway/ipstack-access-key"

    FederalDepositSecrets: "prod/vendorgateway/federal-deposit-secrets"

    # Shipway username and password
    ShipwayUsername: "prod/vendorgateway/shipway-username"
    ShipwayPassword: "prod/vendorgateway/shipway-password"

    # client api AA keys
    AaVgSecretsV1: "prod/vendorgateway/aa-sahamati-secrets-v1"
    AaVgVnSecretsV1: "prod/vg-vn/aa-secrets-v1"

    # Experian Credit Report
    ExperianCreditReportPresenceClientName: "prod/vendorgateway/experian-credit-report-presence-client-name"
    ExperianCreditReportFetchClientName: "prod/vendorgateway/experian-credit-report-fetch-client-name"
    ExperianCreditReportForExistingUserClientName: "prod/vendorgateway/experian-credit-report-for-existing-user-client-name"
    ExperianExtendSubscriptionClientName: "prod/vendorgateway/experian-extend-subscription-client-name"
    ExperianVoucherCode: "prod/vendorgateway/experian-credit-report-voucher-code"

    # Experian Secrets
    ExperianSecrets: "prod/vendorgateway/experian-secrets"

    # cvl secrets
    CvlSftpUser: "prod/vendorgateway/cvl-sftp-user"
    CvlSftpPass: "prod/vendorgateway/cvl-sftp-pass"
    CvlKraPassKey: "prod/vendorgateway/cvl-kra-pass-key"
    CvlKraPosCode: "prod/vendorgateway/cvl-kra-pos-code"
    CvlKraUserName: "prod/vendorgateway/cvl-kra-user-name"
    CvlKraPassword: "prod/vendorgateway/cvl-kra-password"
    CvlSftpSshKey: "prod/vendorgateway/cvl-sftp-ssh-key"
    CvlSecrets: "prod/vendorgateway/cvl-secrets"

    #digilocker
    DigilockerClientSecret: "prod/vendorgateway/digilocker-client-secret"

    # nsdl secrets
    NsdlUserId: "prod/vendorgateway/nsdl-user-id"

    # Manch secrets
    ManchSecureKey: "prod/vendorgateway/manch-secure-key"
    ManchTemplateKey: "prod/vendorgateway/manch-template-key"

    #ckyc
    CkycFiCode: "prod/vendorgateway/ckyc-fi-code"

    SeonClientApiKey: "prod/vendorgateway/seon-api-key"

    CAMSKey: "prod/investment-vendorgateway/cams-key"

    KarvyKey: "prod/investment-vendorgateway/karvy-key"

    SmallCaseKey: "prod/vendorgateway/smallcase-key"

    MFCentralKey: "prod/vendorgateway/mfcentral-key"

    #GPlace api key
    GPlaceApiKey: "prod/vendorgateway/gplace-api-key"

    #liquiloans secrets
    LiquiloansSecrets: "prod/vendorgateway/liquiloans-secrets"

    # Lending keys
    PreApprovedLoanFederalSecrets: "prod/vendorgateway/lending-preapprovedloans-secrets"
    FederalSftpSshKey: "prod/vendorgateway/federal-sftp-ssh-key"
    PreApprovedLoanSecrets: "prod/vendorgateway/lending-preapprovedloans-secrets"

    # Leegality
    LeegalitySecret: "prod/vendorgateway/esign-leegality-secrets"

    # karza api keys
    KarzaKey: "prod/vendorgateway/karza-key"
    #    TartanKey: "prod/vendorgateway/tartan-key"

    VKYCAgentDashboardSecrets: "prod/vendorgateway/vkyc-agent-dash"

    GeolocationKey: "prod/vendorgateway/geolocation-key"

    # Secrets of payu
    PayuToken: "prod/vendorgateway/payu-token"
    PayuApiKey: "prod/vendorgateway/payu-key"

    MaxmindSecrets: "prod/vendorgateway/maxmind-secrets"

    BureauSecrets: "prod/vendorgateway/bureau-secrets"

    # DronaPay
    DronaPayKey: "prod/vendorgateway/dronapay-key"

    SignzySecrets: 'prod/vendorgateway/signzy-secrets'

    AlpacaSecrets: "prod/vendorgateway/alpaca-secrets"

    FederalInternationalFundTransferSecrets: "prod/vendorgateway/federal-internationalfundtransfer-secrets"

    FederalProfileValidationSecrets: 'prod/vendorgateway/hunter-secrets'

    # p2p Investment secrets
    p2pInvestmentLiquiloansSftpUser: "prod/vendorgateway/p2p-investment-liquiloans-sftp-user"
    p2pInvestmentLiquiloansSftpPassword: "prod/vendorgateway/p2p-investment-liquiloans-sftp-pass"

    MorningStarSecrets: "prod/vendorgateway/morningstar-secrets"
    MorningStarAccountSecrets: "prod/vendorgateway/morningstar-account-secrets"

    DepositInterestRateInfoSecrets: "prod/vendorgateway/deposit-interest-rate-info-secrets"

    TssApiToken: "prod/vendorgateway/tss-api-token"
    TSSAPITokenForSG: "prod/vendorgateway/tss-api-token-sg"

    VistaraSecrets: "prod/vendorgateway/vistara-secrets"

    SlackSecrets: "prod/vendorgateway/slack-bot-tokens"

    # Fennel Secrets
    FennelFeatureStoreSecrets: "prod/vendorgateway/fennel-secrets"

    LeadSquaredSecrets: "prod/vendorgateway/leadsquared-keys"

    DreamfolksSecrets: "prod/vendorgateway/dreamfolks-secrets"

    # Lentra secrets
    LentraSecrets: "prod/vendorgateway/lentra-secrets"

    EpifiFederalEpanSftpSecrets: "prod/vendorgateway/epifi-federal-epan-sftp-secrets"

    EpifiFederalEnachSftpSecrets: "prod/vendorgateway/epifi-federal-enach-sftp-secrets"

    CredgenicsAuthToken: "prod/vendorgateway/credgenics"

    # IDFC loans private key
    FiIdfcPreApprovedLoanPrivateKey: "prod/vendorgateway/fi-idfc-pre-approved-loan-private-key"

    LendingMFCentralSecrets: 'prod/vendorgateway/lamf-secrets'

    LendingFiftyFinLamfSecrets: 'prod/vendorgateway/fiftyfin-lamf-secrets'

    KarzaPanProfileKey: "prod/vendorgateway/pan-profile-karza-key"

    PoshvineSecrets: "prod/vendorgateway/poshvine-secrets"

    CibilSecrets: "prod/vendorgateway/cibil"

    PanValidationSecretskey: "prod/vendorgateway/panvalidationsecrets"

    AclSftpSecretKey: "prod/sftp/vendors/acl/password"

    BureauIdSecrets: "prod/vendorgateway/bureauid-secrets"

    NetCoreEpifiSecrets: "prod/vendorgateway/netcore-epifi-secrets"

    VisaSecrets: "prod/vendorgateway/visa-secrets"

    MoEngageSecrets: "prod/vendorgateway/moengage-secrets"

    ScienapticSecrets: "prod/vendorgateway/openresty-scienaptic-sms"

    PerfiosDigilockerSecrets: "prod/vendorgateway/perfios-digilocker-secrets"

    CredgenicsAuthenticationKeyV2: "prod/vendorgateway/credgenics-v2"

    AirtelFedSMSSecrets: "prod/vendorgateway/airtel-fed-sms-secrets"

    AirtelEpifiSMSSecrets: "prod/vendorgateway/airtel-epifi-sms-secrets"

#    SetuBillPaySecrets: "prod/vendorgateway/setu-billpay-secrets"
    SetuMobileRechargeSecrets: "prod/vendorgateway/setu-mobile-recharge-secrets"

    FederalEscalationSecrets: "prod/vendorgateway/federal-io-secrets"
    FederalEscalationClientSslCert: "prod/vendorgateway/federal-io-cert"
    FederalEscalationClientSslKey: "prod/vendorgateway/federal-io-key"
    SavenSecrets: "prod/vendorgateway/saven-cc-secrets"

    BridgewiseSecrets: "prod/vendorgateway/bridgewise-secrets"

    NuggetSecrets: "prod/vendorgateway/nugget-secrets"

DisputeSFTP:
  user: ""
  password: ""
  host: ""
  port: 80
  S3BucketName: "epifi-federal-disputes"

Flags:
  TrimDebugMessageFromStatus: false
  TokenValidation: true
  UseCustomTrustedCertPool: true
  AllowSpecialCharactersInAddress: false
  UseNewOccupationInCifCreation: true
  UseNewFieldsInCifCreation: true
  UseNewFieldsInAccountCreation: true
  EnableTransactionEnquiryNewApi: true
  EnableUATForVKYC: false
  EnableInstrumentBillingInterceptor: true
  DisableGstReportingForIFT: false
  EnableFennelClusterV3: true
  EnableCibilV2Secrets: true

SecureLogging:
  EnableSecureLog: true
  SecureLogPath: "/var/log/vendorgateway/secure.log"
  MaxSizeInMBs: 50
  MaxBackups: 20

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "redis-12000.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:12000"
    ClientName: vendorgateway
  AuthDetails:
    SecretPath: "prod/redis/vendorgateway/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"

Freshdesk:
  GroupEnumToGroupIdMapping:
    CALLBACK: ***********
    EPIFI_ESCALATION: ***********
    ESCALATED_CASES_CLOSURE: ***********
    FEDERAL_ESCALATIONS: ***********
    L1_SUPPORT: ***********
    L2_SUPPORT: ***********
    NON_SFTP_ESCALATIONS: ***********
    SFTP_ESCALATIONS: ***********
    SFTP_PENDING_GROUP: ***********
    FEDERAL_UPDATES: ***********
    L1_SUPPORT_WAITLIST: ***********
    GROUP_RISK_OPS: ***********
    GROUP_ACCOUNT_CLOSURE_RISK_BLOCK: ***********
    GROUP_L1_SUPPORT_CALL: ***********
    GROUP_L1_SUPPORT_CHAT: ***********
    GROUP_L1_SUPPORT_EMAIL: ***********
    GROUP_L1_SUPPORT_SOCIAL_MEDIA: ***********
    GROUP_OUTBOUND_CALL_BACK: ***********
    GROUP_LOAN_OUTBOUND_CALL: ***********
  ProductCategoryEnumToValueMapping:
    TRANSACTION: "Transactions"
    ACCOUNTS: "Accounts"
    ONBOARDING: "Onboarding"
    SAVE: "Save"
    WAITLIST: "Waitlist"
    RE_ONBOARDING: "Re-onboarding"
    PRODUCT_CATEGORY_REWARDS: "Rewards"
    PRODUCT_CATEGORY_FIT: "FIT"
    PRODUCT_CATEGORY_DEBIT_CARD: "Debit Card"
    PRODUCT_CATEGORY_REFERRALS: "Referrals"
    PRODUCT_CATEGORY_CONNECTED_ACCOUNTS: "Connected Accounts"
    PRODUCT_CATEGORY_FRAUD_AND_RISK: "Fraud & Risk"
    PRODUCT_CATEGORY_JUMP_P2P: "Jump P2P"
    PRODUCT_CATEGORY_PROFILE: "Profile"
    PRODUCT_CATEGORY_SALARY_ACCOUNT: "Salary account"
    PRODUCT_CATEGORY_SEARCH: "Search"
    PRODUCT_CATEGORY_WEALTH_ONBOARDING: "Wealth Onboarding"
    PRODUCT_CATEGORY_WEALTH_MUTUAL_FUNDS: "Wealth Mutual Funds"
    PRODUCT_CATEGORY_APP_CRASH: "App Crash"
    PRODUCT_CATEGORY_DATA_DELETION: "Data deletion"
    PRODUCT_CATEGORY_SCREENER: "Screener"
    PRODUCT_CATEGORY_GOOGLE_TOKEN_EXPIRED: "Google Token expired"
    PRODUCT_CATEGORY_LANGUAGE_CALLBACK: "Language callback"
    PRODUCT_CATEGORY_CATEGORY_NOT_FOUND: "Category not found"
    PRODUCT_CATEGORY_KYC_OUTCALL: "KYC Outcall"
    PRODUCT_CATEGORY_TRANSACTION_ISSUES: "Transaction Issues"
    PRODUCT_CATEGORY_REWARDS_NEW: "Rewards New"
    PRODUCT_CATEGORY_REFERRALS_NEW: "Referrals New"
    PRODUCT_CATEGORY_GENERAL_ENQUIRIES_ABOUT_FI: "General enquiries about Fi"
    PRODUCT_CATEGORY_NO_RESPONSE_OR_BLANK_CHAT: "No response/ Blank chat"
    PRODUCT_CATEGORY_CALL_DROP_OR_DISCONNECTED: "Call drop/ disconnected"
    PRODUCT_CATEGORY_INSTANT_LOANS: "Instant Loans"
    PRODUCT_CATEGORY_TIERING_PLANS: "Tiering plans"
    PRODUCT_CATEGORY_CREDIT_CARD: "Credit Card"
    PRODUCT_CATEGORY_US_STOCKS: "US stocks"
    PRODUCT_CATEGORY_DEVICE: "Device"
    PRODUCT_CATEGORY_RISK: "Risk"
    PRODUCT_CATEGORY_ON_APP_TRANSACTIONS: "In-App Transactions"
    PRODUCT_CATEGORY_OFF_APP_TRANSACTIONS: "Off-App Transactions"
    PRODUCT_CATEGORY_INSTANT_SALARY: "Instant Salary"
    PRODUCT_CATEGORY_SIMPLIFI_CREDIT_CARD: "SimpliFi Credit Card"
    PRODUCT_CATEGORY_LAMF: "LAMF"
    PRODUCT_CATEGORY_MAGNIFI_CREDIT_CARD: "MagniFi Credit Card"
    PRODUCT_CATEGORY_SALARY_LITE: "Salary Lite"
    PRODUCT_CATEGORY_FI_STORE: "Fi-Store"
    PRODUCT_CATEGORY_GENERAL_ENQUIRY: "General Enquiry"
    PRODUCT_CATEGORY_APP_RELATED: "App Related"
    PRODUCT_CATEGORY_DEPOSITS_AND_INVESTMENTS: "Deposits & Investments"
    PRODUCT_CATEGORY_INCOMPLETE_CONVERSATION: "Incomplete Conversation"
    PRODUCT_CATEGORY_LOANS: "Loans"
    PRODUCT_CATEGORY_NET_WORTH: "Net Worth"
    PRODUCT_CATEGORY_SERVICE_REQUESTS: "Service Requests"
  TransactionTypeEnumToValueMapping:
    DEBIT_CARD: "Debit Card"
    IMPS: "IMPS"
    NEFT: "NEFT"
    RTGS: "RTGS"
    UPI: "UPI"
    ECOM: "Unknown"
    POS_ATM: "Unknown"
    INTRA_BANK: "Intra Bank"
    TRANSACTION_TYPE_UNKNOWN: "Unknown"
  DisputeStatusEnumToValueMapping:
    ACCEPTED: "Accepted"
    REJECTED: "Rejected"
  StatusEnumToValueMapping:
    STATUS_UNSPECIFIED: 0
    OPEN: 2
    PENDING: 3
    RESOLVED: 4
    CLOSED: 5
    WAITING_ON_THIRD_PARTY: 7
    ESCALATED_TO_L2: 8
    ESCALATED_TO_FEDERAL: 11
    SEND_TO_PRODUCT: 24
    WAITING_ON_PRODUCT: 25
    REOPEN: 26
    NEEDS_CLARIFICATION_FROM_CX: 27
    WAITING_ON_CUSTOMER: 12
  ProductCategoryDetailsEnumToValueMapping:
    MANUAL_WHITELISTING: "Manual Whitelisting"
    APP_DOWNLOAD_ISSUE: "App download issue"
    DEVICE_CHECK_FAILURE: "Device check failure"
    PHONE_NUMBER_OTP: "Phone number OTP"
    EMAIL_SELECTION_FAILURE: "Email selection failure"
    MOTHER_FATHER_NAME: "Mother Father Name"
    PAN_NAME_VALIDATION_FAILURE: "PAN Name validation failure"
    EXISTING_FEDERAL_ACCOUNT: "Existing Federal account"
    KYC: "KYC"
    LIVENESS: "Liveness"
    FACEMATCH_FAIL: "Face-match fail"
    UN_NAME_CHECK: "UN Name check"
    CONFIRM_CARD_MAILING_ADDRESS: "Confirm Card Mailing address"
    UPI_CONSENT_FAILURE: "UPI Consent failure"
    DEVICE_REGISTRATION_FAILURE: "Device Registration Failure"
    CUSTOMER_CREATION_FAILURE: "Customer creation failure"
    ACCOUNT_OPENING_DELAYED: "Account opening delayed"
    CARD_CREATION_FAILURE: "Card creation failure"
    CARD_PIN_SET_FAILURE: "Card PIN set failure"
    UPI_SETUP_FAILURE: "UPI setup failure"
    VKYC: "VKYC"
    REONBOARDING: "Re-onboarding"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_PIN: "PIN"
    PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_ACTIVATION: "Activation"
    PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_DELIVERY: "Delivery"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP: "Debited via Fi app  but"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP: "Debited from FI account (via Other App) but"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_MIN_KYC_EXPIRY: "Min KYC expiry"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_CARDS_ATM: "Cards - ATM"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT: "UPI - Unable to transact"
    PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL: "Investment Transaction Successful"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CLOSURE_REQUEST: "Account Closure Request"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_OPENING_ISSUES: "Account Opening Issues"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_UPGRADE_DOWNGRADE: "Account Upgrade/Downgrade"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_BALANCE_TRANSFER: "Balance Transfer"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CATEGORY_NOT_FOUND: "Category Not Found"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CHEQUEBOOK_RELATED: "Chequebook Related"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_DORMANT: "Dormant"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_FEES_AND_CHARGES: "Fees & Charges"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_KYC_RELATED: "KYC Related"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_LIEN_ON_ACCOUNT: "Lien On Account"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_RE_LOGIN_ISSUES: "Re Login Issues"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_SALARY_PROGRAM: "Salary Program"
    PRODUCT_CATEGORY_DETAILS_APP_RELATED_ISSUES: "App Related Issues"
    PRODUCT_CATEGORY_DETAILS_BANK_INCOMING: "Bank Incoming"
    PRODUCT_CATEGORY_DETAILS_CX_INCOMING: "CX Incoming"
    PRODUCT_CATEGORY_DETAILS_BLOCK_PERMANENTLY: "Block Permanently"
    PRODUCT_CATEGORY_DETAILS_CARD_REQUEST: "Card Request"
    PRODUCT_CATEGORY_DETAILS_CARD_SETTINGS: "Card Settings"
    PRODUCT_CATEGORY_DETAILS_FIT_RULES: "FIT Rules"
    PRODUCT_CATEGORY_DETAILS_JUMP: "Jump"
    PRODUCT_CATEGORY_DETAILS_MF_INVESTMENTS: "MF Investments"
    PRODUCT_CATEGORY_DETAILS_MF_ONBOARDING: "MF Onboarding"
    PRODUCT_CATEGORY_DETAILS_MF_WITHDRAWALS: "MF Withdrawals"
    PRODUCT_CATEGORY_DETAILS_US_STOCKS: "US Stocks"
    PRODUCT_CATEGORY_DETAILS_US_STOCKS_WALLET_ISSUES: "US Stocks Wallet Issues"
    PRODUCT_CATEGORY_DETAILS_DEPRECATED_PRODUCT: "Deprecated Product"
    PRODUCT_CATEGORY_DETAILS_BLANK_CHAT: "Blank Chat"
    PRODUCT_CATEGORY_DETAILS_CALL_DROP_DISCONNECTED: "Call Drop/Disconnected"
    PRODUCT_CATEGORY_DETAILS_INCOMPLETE_EMAIL: "Incomplete Email"
    PRODUCT_CATEGORY_DETAILS_LOAN_APPLICATION_DISBURSAL: "Application/Disbursal Issue"
    PRODUCT_CATEGORY_DETAILS_LOAN_BUREAU_CIBIL: "Bureau/Cibil"
    PRODUCT_CATEGORY_DETAILS_LOAN_COLLECTIONS: "Collections"
    PRODUCT_CATEGORY_DETAILS_LOAN_EMI_RELATED: "EMI Related Issues"
    PRODUCT_CATEGORY_DETAILS_LOAN_LAMF_SHORTFALL: "LAMF Shortfall"
    PRODUCT_CATEGORY_DETAILS_LOAN_CLOSURE_REQUEST: "Loan Closure Request/Issues"
    PRODUCT_CATEGORY_DETAILS_LOAN_OUTCALLING: "Outcalling"
    PRODUCT_CATEGORY_DETAILS_LOAN_PERSONAL_DETAILS: "Personal Details Updatation"
    PRODUCT_CATEGORY_DETAILS_LOAN_REFUND_WAIVER: "Refund/Waiver Request"
    PRODUCT_CATEGORY_DETAILS_UNABLE_TO_CONNECT: "Unable To Connect"
    PRODUCT_CATEGORY_DETAILS_UNABLE_TO_DISCONNECT: "Unable to Disconnect"
    PRODUCT_CATEGORY_DETAILS_REWARDS_FI_POINTS_NOT_REFUNDED: "Fi Points Not Refunded"
    PRODUCT_CATEGORY_DETAILS_REWARDS_GIFT_CARDS: "Gift Cards"
    PRODUCT_CATEGORY_DETAILS_REWARDS_INCORRECT_REWARD: "Incorrect Reward Received"
    PRODUCT_CATEGORY_DETAILS_REWARDS_NOT_RECEIVED: "Reward Not Received"
    PRODUCT_CATEGORY_DETAILS_BANK_INITIATED_FREEZE: "Bank Initated Freeze"
    PRODUCT_CATEGORY_DETAILS_INVESTMENT_WITHDRAWALS: "Investment Withdrawals"
    PRODUCT_CATEGORY_DETAILS_LEA_NPCI_COMPLAINT: "LEA/NPCI Complaint"
    PRODUCT_CATEGORY_DETAILS_CALLBACK_REQUEST: "Callback Request"
    PRODUCT_CATEGORY_DETAILS_DATA_DELETION: "Data Deletion"
    PRODUCT_CATEGORY_DETAILS_NACH_AND_MANDATES: "Nach & Mandates"
    PRODUCT_CATEGORY_DETAILS_REVOKE_APP_ACCESS: "Revoke App Access"
    PRODUCT_CATEGORY_DETAILS_STOP_SERVICES: "Stop Services"
    PRODUCT_CATEGORY_DETAILS_AMOUNT_DEBITED: "Amount Debited"
    PRODUCT_CATEGORY_DETAILS_AMOUNT_DEBITED_NOT_CREDITED: "Amount Debited But Not Credited"
    PRODUCT_CATEGORY_DETAILS_AUTOMATED_PAYMENTS: "Automated Payments"
    PRODUCT_CATEGORY_DETAILS_UNAUTHORISED_FRAUD_TRANSACTIONS: "Unauthorised/Fraud Transactions"
    PRODUCT_CATEGORY_DETAILS_CHEQUE_TRANSACTION: "Cheque Transaction"
    PRODUCT_CATEGORY_DETAILS_DATA_NOT_REFRESHED: "Data Not Refreshed"
    PRODUCT_CATEGORY_DETAILS_BUYING_US_STOCKS: "Buying US Stocks"
    PRODUCT_CATEGORY_DETAILS_BUSINESS_COLLABORATION: "Business Collaboration"
    PRODUCT_CATEGORY_DETAILS_NET_BANKING: "Net Banking"
    PRODUCT_CATEGORY_DETAILS_UNREGISTERED_USER: "Unregistered User"
    PRODUCT_CATEGORY_DETAILS_UNABLE_TO_PAY: "Unable To Pay"
    PRODUCT_CATEGORY_DETAILS_CREDIT_PENDING_TO_FI: "Credit Pending To Fi"
    PRODUCT_CATEGORY_DETAILS_DOCUMENT_REQUEST: "Document Request"
  SubCategoryDetailsEnumToValueMapping:
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_NEW: "New"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_APPROVED: "Approved"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_REJECTED: "Rejected"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_ON_HOLD: "On-Hold"
    SUB_CATEGORY_PIN_UPI_PIN: "UPI PIN"
    SUB_CATEGORY_PIN_DEVICE_PIN: "Device PIN"
    SUB_CATEGORY_PIN_APP_PIN: "App PIN"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_QR_CODE_NOT_WORKING: "QR code not working"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_UNABLE_TO_SET_PIN: "Unable to set PIN"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_POS: "Cannot enable POS"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_CONTACTLESS: "Cannot enable Contactless"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_ATM_WITHDRAWAL: "Cannot enable ATM withdrawal"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_OTP_NOT_RECEIVED: "OTP not received"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_HOW_TO_ACTIVATE: "How to activate"
    SUB_CATEGORY_DEBIT_CARD_DELIVERY_TRACKING: "Tracking"
    SUB_CATEGORY_DEBIT_CARD_DELIVERY_DID_NOT_RECEIVED_CARD: "Did not receive card"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_MERCHANT: "Not credited to merchant"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_BENEFICIARY: "Not credited to beneficiary"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_MERCHANT: "Not credited to merchant"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_BENEFICIARY: "Not credited to beneficiary"
    SUB_CATEGORY_ACCOUNTS_MIN_KYC_EXPIRY_BALANCE_REFUND: "Balance refund"
    SUB_CATEGORY_TRANSACTIONS_CARDS_ATM_DEBITED_BUT_NOT_DISPENSED_AT_MACHINE: "Debited but not dispensed at machine"
    SUB_CATEGORY_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT_UPI_PIN_TRIES_EXCEEDED: "UPI pin tries exceeded"
    # Account Closure Request L3 categories
    SUB_CATEGORY_ACCOUNTS_CLOSURE_FREEZE_ON_ACCOUNT: "Freeze On Account"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_IN_APP_REQUEST_RECEIVED: "In App Request Received (Auto ID)"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_MANUAL_REQUEST: "Manual Request"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_NOT_CLOSABLE_DUE_TO_PENDING_CHARGES: "Not Closable Due To Pending Charges"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_REDIRECTED_TO_APP: "Redirected To App"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_FULL_KYC_ACCOUNT_CLOSED: "Full Kyc Account Closed"

    # Account Opening Issues L3 categories
    SUB_CATEGORY_ACCOUNTS_OPENING_ADD_FUNDS_ON_APP: "Add Funds On App"
    SUB_CATEGORY_ACCOUNTS_OPENING_APP_DOWNLOAD: "App Download"
    SUB_CATEGORY_ACCOUNTS_OPENING_DEVICE_REGISTRATION_FAILURE: "Device Registration Failure"
    SUB_CATEGORY_ACCOUNTS_OPENING_CARD_CREATION_PIN_SETUP_FAILURE: "Card Creation & Pin Setup Failure"
    SUB_CATEGORY_ACCOUNTS_OPENING_CONSENT_RELATED: "Consent Related"
    SUB_CATEGORY_ACCOUNTS_OPENING_CUSTOMER_CREATION: "Customer Creation"
    SUB_CATEGORY_ACCOUNTS_OPENING_EXISTING_FEDERAL_ACCOUNT: "Existing Federal Account"
    SUB_CATEGORY_ACCOUNTS_OPENING_KYC_RELATED: "KYC Related"
    SUB_CATEGORY_ACCOUNTS_OPENING_LIVENESS_FACEMATCH_ISSUE: "Liveness & Facematch Issue"
    SUB_CATEGORY_ACCOUNTS_OPENING_APP_SCREENING: "App Screening"
    SUB_CATEGORY_ACCOUNTS_OPENING_REFUND_FOR_ADD_FUNDS: "Refund For Add Funds"
    SUB_CATEGORY_ACCOUNTS_OPENING_REOPEN_CLOSED_ACCOUNT: "Reopen Closed Account"
    SUB_CATEGORY_ACCOUNTS_OPENING_STUCK_AT_EMAIL_VERIFICATION: "Stuck At Email Verfication"
    SUB_CATEGORY_ACCOUNTS_OPENING_VKYC_ISSUES: "Vkyc Issues"

    # Account Upgrade/Downgrade L3 categories
    SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_FUNDS_ADDED_BUT_ACCOUNT_NOT_UPGRADED: "Funds Added But A/C Not Upgraded"
    SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_TIER_DOWNGRADE: "Tier Downgrade"
    SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_WITHIN_COOL_OFF_PERIOD: "Within Cool Off Period"

    # Account KYC Related L3 categories
    SUB_CATEGORY_ACCOUNTS_KYC_MIN_KYC_EXPIRED: "Min Kyc Expired"
    SUB_CATEGORY_ACCOUNTS_KYC_UNABLE_TO_SUBMIT_FORM: "Unable To Submit Form"
    SUB_CATEGORY_ACCOUNTS_KYC_NOT_UPDATED: "KYC Not Updated"
    SUB_CATEGORY_ACCOUNTS_KYC_UPDATED_BUT_ACCOUNT_NOT_ACTIVATED: "KYC Updated but A/C Not Activated"
    SUB_CATEGORY_ACCOUNTS_KYC_SIGNATURE_NOT_UPDATED: "Signature Not Updated"

    # Card Related L3 categories
    SUB_CATEGORY_CARD_REQUEST_CARD_REPLACEMENT: "Card Replacement"
    SUB_CATEGORY_CARD_REQUEST_CARD_UPGRADE: "Card Upgrade"
    SUB_CATEGORY_CARD_REQUEST_CARD_VARIANT_CHANGE: "Card Variant Change"
    SUB_CATEGORY_CARD_REQUEST_NEW_CARD: "New Card"
    SUB_CATEGORY_CARD_REQUEST_VIRTUAL_CARD: "Virtual Card"

    SUB_CATEGORY_CARD_SETTINGS_CARD_ACTIVATION: "Card Activation"
    SUB_CATEGORY_CARD_SETTINGS_CARD_BLOCK: "Card Block"
    SUB_CATEGORY_CARD_SETTINGS_CARD_DELIVERY: "Card Delivery"
    SUB_CATEGORY_CARD_SETTINGS_CARD_LIMIT: "Card Limit"
    SUB_CATEGORY_CARD_SETTINGS_CARD_PIN: "Card Pin"
    SUB_CATEGORY_CARD_SETTINGS_CARD_UNBLOCK: "Card Unblock"
    SUB_CATEGORY_CARD_SETTINGS_INTERNATIONAL_TRANSACTIONS: "International Transactions"

    SUB_CATEGORY_CARD_CHARGES_ANNUAL_CHARGES: "Annual Charges"
    SUB_CATEGORY_CARD_CHARGES_CARD_REPLACEMENT_CHARGES: "Card Replacement Charges"
    SUB_CATEGORY_CARD_CHARGES_FOREIGN_MARKUP_FEE: "Foreign Markup Fee"
    SUB_CATEGORY_CARD_CHARGES_JOINING_FEE: "Joining Fee"
    SUB_CATEGORY_CARD_CHARGES_LATE_PAYMENT_CHARGES: "Late Payment Charges"
    SUB_CATEGORY_CARD_CHARGES_OVERLIMIT_CHARGES: "Overlimit Charges"
    SUB_CATEGORY_CARD_CHARGES_PROCESSING_FEE: "Processing Fee"

    SUB_CATEGORY_CARD_INFO_CARD_BENEFITS: "Card Benefits"
    SUB_CATEGORY_CARD_INFO_CARD_ELIGIBILITY: "Card Eligibility"
    SUB_CATEGORY_CARD_INFO_CARD_FEATURES: "Card Features"
    SUB_CATEGORY_CARD_INFO_CARD_OFFERS: "Card Offers"
    SUB_CATEGORY_CARD_INFO_CARD_REWARDS: "Card Rewards"
    SUB_CATEGORY_CARD_INFO_CARD_TYPES: "Card Types"

    # ATM Transaction L3 categories
    SUB_CATEGORY_ATM_TRANSACTION_AMOUNT_DEBITED_NOT_DISPENSED: "Amount Debited Not Dispensed"
    SUB_CATEGORY_ATM_TRANSACTION_AMOUNT_DISPENSED_NOT_DEBITED: "Amount Dispensed Not Debited"
    SUB_CATEGORY_ATM_TRANSACTION_CARD_BLOCKED: "Card Blocked"
    SUB_CATEGORY_ATM_TRANSACTION_CARD_CAPTURED: "Card Captured"
    SUB_CATEGORY_ATM_TRANSACTION_INCORRECT_AMOUNT_DISPENSED: "Incorrect Amount Dispensed"
    SUB_CATEGORY_ATM_TRANSACTION_LIMIT_EXCEEDED: "Limit Exceeded"
    SUB_CATEGORY_ATM_TRANSACTION_PIN_BLOCKED: "Pin Blocked"
    SUB_CATEGORY_ATM_TRANSACTION_TRANSACTION_DECLINED: "Transaction Declined"

    # Transaction Issues L3 categories
    SUB_CATEGORY_TRANSACTION_ISSUES_AMOUNT_DEBITED_NOT_CREDITED: "Amount Debited Not Credited"
    SUB_CATEGORY_TRANSACTION_ISSUES_AMOUNT_CREDITED_NOT_DEBITED: "Amount Credited Not Debited"
    SUB_CATEGORY_TRANSACTION_ISSUES_DUPLICATE_TRANSACTION: "Duplicate Transaction"
    SUB_CATEGORY_TRANSACTION_ISSUES_FAILED_TRANSACTION: "Failed Transaction"
    SUB_CATEGORY_TRANSACTION_ISSUES_INCORRECT_AMOUNT: "Incorrect Amount"
    SUB_CATEGORY_TRANSACTION_ISSUES_MERCHANT_DISPUTE: "Merchant Dispute"
    SUB_CATEGORY_TRANSACTION_ISSUES_REFUND_NOT_RECEIVED: "Refund Not Received"
    SUB_CATEGORY_TRANSACTION_ISSUES_UNAUTHORIZED_TRANSACTION: "Unauthorized Transaction"

    # Fixed/Smart Deposit L3 categories
    SUB_CATEGORY_FIXED_DEPOSIT_ACCOUNT_OPENING: "Account Opening"
    SUB_CATEGORY_FIXED_DEPOSIT_INTEREST_PAYOUT: "Interest Payout"
    SUB_CATEGORY_FIXED_DEPOSIT_MATURITY_AMOUNT: "Maturity Amount"
    SUB_CATEGORY_FIXED_DEPOSIT_PREMATURE_WITHDRAWAL: "Premature Withdrawal"
    SUB_CATEGORY_FIXED_DEPOSIT_RENEWAL: "Renewal"
    SUB_CATEGORY_FIXED_DEPOSIT_TDS: "TDS"

    SUB_CATEGORY_SMART_DEPOSIT_ACCOUNT_OPENING: "Account Opening"
    SUB_CATEGORY_SMART_DEPOSIT_INTEREST_PAYOUT: "Interest Payout"
    SUB_CATEGORY_SMART_DEPOSIT_MATURITY_AMOUNT: "Maturity Amount"
    SUB_CATEGORY_SMART_DEPOSIT_PREMATURE_WITHDRAWAL: "Premature Withdrawal"
    SUB_CATEGORY_SMART_DEPOSIT_RENEWAL: "Renewal"
    SUB_CATEGORY_SMART_DEPOSIT_TDS: "TDS"

    # FIT Rules L3 categories
    SUB_CATEGORY_FIT_RULES_CREATION: "Creation"
    SUB_CATEGORY_FIT_RULES_DELETION: "Deletion"
    SUB_CATEGORY_FIT_RULES_EXECUTION: "Execution"
    SUB_CATEGORY_FIT_RULES_MODIFICATION: "Modification"

    # Jump L3 categories
    SUB_CATEGORY_JUMP_ELIGIBILITY: "Eligibility"
    SUB_CATEGORY_JUMP_EXECUTION: "Execution"
    SUB_CATEGORY_JUMP_LIMIT: "Limit"
    SUB_CATEGORY_JUMP_SETTLEMENT: "Settlement"

    # Mutual Funds L3 categories
    SUB_CATEGORY_MUTUAL_FUNDS_ACCOUNT_OPENING: "Account Opening"
    SUB_CATEGORY_MUTUAL_FUNDS_INVESTMENT: "Investment"
    SUB_CATEGORY_MUTUAL_FUNDS_KYC: "KYC"
    SUB_CATEGORY_MUTUAL_FUNDS_REDEMPTION: "Redemption"
    SUB_CATEGORY_MUTUAL_FUNDS_SIP: "SIP"
    SUB_CATEGORY_MUTUAL_FUNDS_STATEMENT: "Statement"
    SUB_CATEGORY_MUTUAL_FUNDS_SWITCH: "Switch"
    SUB_CATEGORY_MUTUAL_FUNDS_TAX: "Tax"

    # US Stocks L3 categories
    SUB_CATEGORY_US_STOCKS_ACCOUNT_OPENING: "Account Opening"
    SUB_CATEGORY_US_STOCKS_BUYING: "Buying"
    SUB_CATEGORY_US_STOCKS_KYC: "KYC"
    SUB_CATEGORY_US_STOCKS_SELLING: "Selling"
    SUB_CATEGORY_US_STOCKS_STATEMENT: "Statement"
    SUB_CATEGORY_US_STOCKS_TAX: "Tax"
    SUB_CATEGORY_US_STOCKS_TRANSFER: "Transfer"
    SUB_CATEGORY_US_STOCKS_WALLET: "Wallet"

    # Fi Store L3 categories
    SUB_CATEGORY_FI_STORE_ACTIVATION: "Activation"
    SUB_CATEGORY_FI_STORE_CANCELLATION: "Cancellation"
    SUB_CATEGORY_FI_STORE_DELIVERY: "Delivery"
    SUB_CATEGORY_FI_STORE_PAYMENT: "Payment"
    SUB_CATEGORY_FI_STORE_REFUND: "Refund"
    SUB_CATEGORY_FI_STORE_RETURN: "Return"

    # Salary Programs L3 categories
    SUB_CATEGORY_SALARY_PROGRAMS_ACTIVATION: "Activation"
    SUB_CATEGORY_SALARY_PROGRAMS_BENEFITS: "Benefits"
    SUB_CATEGORY_SALARY_PROGRAMS_ELIGIBILITY: "Eligibility"
    SUB_CATEGORY_SALARY_PROGRAMS_EMPLOYER: "Employer"
    SUB_CATEGORY_SALARY_PROGRAMS_SALARY_CREDIT: "Salary Credit"

    # Loans L3 categories
    SUB_CATEGORY_LOANS_APPLICATION: "Application"
    SUB_CATEGORY_LOANS_DISBURSEMENT: "Disbursement"
    SUB_CATEGORY_LOANS_DOCUMENTATION: "Documentation"
    SUB_CATEGORY_LOANS_EMI: "EMI"
    SUB_CATEGORY_LOANS_FORECLOSURE: "Foreclosure"
    SUB_CATEGORY_LOANS_INTEREST: "Interest"
    SUB_CATEGORY_LOANS_PREPAYMENT: "Prepayment"
    SUB_CATEGORY_LOANS_STATEMENT: "Statement"

    # Assets L3 categories
    SUB_CATEGORY_ASSETS_BALANCE: "Balance"
    SUB_CATEGORY_ASSETS_CATEGORIZATION: "Categorization"
    SUB_CATEGORY_ASSETS_LINKING: "Linking"
    SUB_CATEGORY_ASSETS_REFRESH: "Refresh"
    SUB_CATEGORY_ASSETS_SYNC: "Sync"
    SUB_CATEGORY_ASSETS_UNLINKING: "Unlinking"

    # Rewards L3 categories
    SUB_CATEGORY_REWARDS_CASHBACK: "Cashback"
    SUB_CATEGORY_REWARDS_FI_COINS: "Fi Coins"
    SUB_CATEGORY_REWARDS_GIFT_CARDS: "Gift Cards"
    SUB_CATEGORY_REWARDS_OFFERS: "Offers"
    SUB_CATEGORY_REWARDS_POINTS: "Points"
    SUB_CATEGORY_REWARDS_VOUCHERS: "Vouchers"

    # Account Security L3 categories
    SUB_CATEGORY_ACCOUNT_SECURITY_BIOMETRIC: "Biometric"
    SUB_CATEGORY_ACCOUNT_SECURITY_DEVICE: "Device"
    SUB_CATEGORY_ACCOUNT_SECURITY_EMAIL: "Email"
    SUB_CATEGORY_ACCOUNT_SECURITY_MOBILE: "Mobile"
    SUB_CATEGORY_ACCOUNT_SECURITY_PASSWORD: "Password"
    SUB_CATEGORY_ACCOUNT_SECURITY_PIN: "PIN"

    # Language Support L3 categories
    SUB_CATEGORY_LANGUAGE_SUPPORT_APP: "App"
    SUB_CATEGORY_LANGUAGE_SUPPORT_COMMUNICATION: "Communication"
    SUB_CATEGORY_LANGUAGE_SUPPORT_DOCUMENTS: "Documents"
    SUB_CATEGORY_LANGUAGE_SUPPORT_SUPPORT: "Support"

    # Data and Statements L3 categories
    SUB_CATEGORY_DATA_STATEMENTS_ACCOUNT_STATEMENT: "Account Statement"
    SUB_CATEGORY_DATA_STATEMENTS_INTEREST_CERTIFICATE: "Interest Certificate"
    SUB_CATEGORY_DATA_STATEMENTS_LOAN_STATEMENT: "Loan Statement"
    SUB_CATEGORY_DATA_STATEMENTS_TAX_STATEMENT: "Tax Statement"
    SUB_CATEGORY_DATA_STATEMENTS_TDS_CERTIFICATE: "TDS Certificate"

    # Mandates L3 categories
    SUB_CATEGORY_MANDATES_ACTIVATION: "Activation"
    SUB_CATEGORY_MANDATES_CANCELLATION: "Cancellation"
    SUB_CATEGORY_MANDATES_MODIFICATION: "Modification"
    SUB_CATEGORY_MANDATES_REGISTRATION: "Registration"
    SUB_CATEGORY_MANDATES_STATUS: "Status"

    # Profile Updates L3 categories
    SUB_CATEGORY_PROFILE_UPDATES_ADDRESS: "Address"
    SUB_CATEGORY_PROFILE_UPDATES_DOB: "DOB"
    SUB_CATEGORY_PROFILE_UPDATES_EMAIL: "Email"
    SUB_CATEGORY_PROFILE_UPDATES_MOBILE: "Mobile"
    SUB_CATEGORY_PROFILE_UPDATES_NAME: "Name"
    SUB_CATEGORY_PROFILE_UPDATES_PAN: "PAN"

    # Device Issues L3 categories
    SUB_CATEGORY_DEVICE_ISSUES_APP_CRASH: "App Crash"
    SUB_CATEGORY_DEVICE_ISSUES_APP_HANG: "App Hang"
    SUB_CATEGORY_DEVICE_ISSUES_BIOMETRIC: "Biometric"
    SUB_CATEGORY_DEVICE_ISSUES_CAMERA: "Camera"
    SUB_CATEGORY_DEVICE_ISSUES_LOCATION: "Location"
    SUB_CATEGORY_DEVICE_ISSUES_NOTIFICATION: "Notification"

    # Transaction Types L3 categories
    SUB_CATEGORY_TRANSACTION_TYPES_ATM: "ATM"
    SUB_CATEGORY_TRANSACTION_TYPES_BILL_PAYMENT: "Bill Payment"
    SUB_CATEGORY_TRANSACTION_TYPES_CARD_PAYMENT: "Card Payment"
    SUB_CATEGORY_TRANSACTION_TYPES_CASH_DEPOSIT: "Cash Deposit"
    SUB_CATEGORY_TRANSACTION_TYPES_CASH_WITHDRAWAL: "Cash Withdrawal"
    SUB_CATEGORY_TRANSACTION_TYPES_CHEQUE: "Cheque"
    SUB_CATEGORY_TRANSACTION_TYPES_FD: "FD"
    SUB_CATEGORY_TRANSACTION_TYPES_IMPS: "IMPS"
    SUB_CATEGORY_TRANSACTION_TYPES_NEFT: "NEFT"
    SUB_CATEGORY_TRANSACTION_TYPES_RTGS: "RTGS"
    SUB_CATEGORY_TRANSACTION_TYPES_UPI: "UPI"

    # UPI Issues L3 categories
    SUB_CATEGORY_UPI_ISSUES_ACTIVATION: "Activation"
    SUB_CATEGORY_UPI_ISSUES_DEACTIVATION: "Deactivation"
    SUB_CATEGORY_UPI_ISSUES_LIMIT: "Limit"
    SUB_CATEGORY_UPI_ISSUES_LINKING: "Linking"
    SUB_CATEGORY_UPI_ISSUES_PIN: "PIN"
    SUB_CATEGORY_UPI_ISSUES_QR: "QR"
    SUB_CATEGORY_UPI_ISSUES_TRANSACTION: "Transaction"
    SUB_CATEGORY_UPI_ISSUES_VPA: "VPA"

  OsTypeEnumToValueMapping:
    ANDROID: "Android"
    IOS: "iOS"
  ResolutionModeEnumToValueMapping:
    RESOLUTION_MODE_AUTO_RESOLUTION: "Auto Resolution"
    RESOLUTION_MODE_BULK_RESOLUTION: "Bulk Resolution"
    RESOLUTION_MODE_MANUAL_RESOLUTION: "Manual Resolution"
    RESOLUTION_MODE_WATSON_RESOLUTION: "Watson Resolution"
  TicketVisibilityEnumToValueMapping:
    TICKET_VISIBILITY_ONLY_AGENT: "Agent"
    TICKET_VISIBILITY_ONLY_CUSTOMER: "Customer"
    TICKET_VISIBILITY_ALL: "All"
  SavingsAccountBalanceEnumToValueMapping:
    SAVINGS_ACCOUNT_BALANCE_LESS_THAN_1: "Less than 1"
    SAVINGS_ACCOUNT_BALANCE_OTHER: "Other"
  MonorailRaisedEnumToValueMapping:
    MONORAIL_RAISED_YES: "Yes"
    MONORAIL_RAISED_NO: "No"
  BooleanEnumToYesNoMapping:
    TRUE: "Yes"
    FALSE: "No"
  DefaultPageSize: 100

FcmAnalyticsLabel: "prod-push-notification"

AwsSes:
  FiMoneyArn: "arn:aws:ses:ap-south-1:************:identity/fi.money"
  FiCareArn: "arn:aws:ses:ap-south-1:************:identity/fi.care"
  StockGuardianArn: "arn:aws:ses:ap-south-1:************:identity/stockguardian.in"
  ConfigSet: "prod_config_set"

TimeoutConfig:
  DefaultTimeout: 30s
  Vendors:
    FEDERAL_BANK:
      vendorgateway_openbanking_auth_vendorauth_reactivatedevice:
        Timeout: 60s
      vendorgateway_openbanking_auth_vendorauth_registerdevice:
        Timeout: 60s
        UseParentContext: true
      vendorgateway_openbanking_auth_vendorauth_authfactorupdate:
        Timeout: 60s
      vendorgateway_openbanking_auth_vendorauth_deregisterdevice:
        Timeout: 60s
      vendorgateway_ckyc_ckyc_search:
        Timeout: 7s
      vendorgateway_ckyc_ckyc_getdata:
        Timeout: 7s
      # due to consective calls to the vg api (first call resulting in timeout), it is leading to race condition at vendors end
      # for the device key registration (mail thread: Device Key Not Registered)
      vendorgateway_openbanking_auth_partnersdk_getsessionparams:
        Timeout: 90s
      vendorgateway_openbanking_bank_customer_bankcustomer_orderchequebook:
        Timeout: 90s
      vendorgateway_openbanking_bank_customer_bankcustomer_updateprofileatbank:
        UseParentContext: true
      vendorgateway_openbanking_bank_customer_bankcustomer_checkprofileupdatestatus:
        UseParentContext: true

      # Setting the timeout for all the dispute APIs post confirmation from federal where
      # createDispute API in DMP system times out if the default time of 30 seconds is kept
      # refer mail thread: DMP API Failure Scenarios
      vendorgateway_openbanking_dispute_dispute_createdispute:
        Timeout: 120s
      vendorgateway_openbanking_dispute_dispute_getdisputestatus:
        Timeout: 120s
      vendorgateway_openbanking_dispute_dispute_senddocument:
        Timeout: 120s
      vendorgateway_openbanking_dispute_dispute_channelquestionnaire:
        Timeout: 120s
      vendorgateway_openbanking_dispute_dispute_accounttransactions:
        Timeout: 120s
      vendorgateway_lending_preapprovedloan_preapprovedloan_getinstantloanclosureenquiry:
        Timeout: 120s
      vendorgateway_lending_creditline_creditline_fetchcombinedcreditlimit:
        Timeout: 60s

    QWIKCILVER:
      vendorgateway_offers_qwikcilver_qwikcilver_createorder:
        Timeout: 45s
    CAMS:
      vendorgateway_wealth_mutualfund_mutualfund_processorderfeedfilesync:
        Timeout: 60s
    KARZA:
      vendorgateway_extvalidate_verifybankaccount:
        Timeout: 60s
        UseParentContext: true
      vendorgateway_employment_employment_epfgetpassbookv2:
        Timeout: 240s
        UseParentContext: true
      vendorgateway_employment_employment_epfgetotp:
        Timeout: 280s
        UseParentContext: true
      vendorgateway_pan_pan_employmentverificationadvanced:
        Timeout: 120s
        UseParentContext: true
    LIQUILOANS:
      vendorgateway_investments_p2p_p2p_getbulkmaturitytransactions:
        Timeout: 180s
    FIFTYFIN:
      vendorgateway_lending_securedloans_fiftyfin_fiftyfin_getloansoastatement_fiftyfin:
        Timeout: 120s
      vendorgateway_lending_securedloans_fiftyfin_fiftyfin_getloanforeclosurestatement_fiftyfin:
        Timeout: 120s

HystrixConfig:
  DefaultServiceTemplate: "Q5"
  Commands:
    FEDERAL_BANK:
      - CommandName: "openbanking_upi_upi_check_txn_status"
        TemplateName: "Q20"
      - CommandName: "openbanking_accounts_accounts_get_account_statement"
        TemplateName: "Q0"
        OverrideTemplateConfig:
          ErrorThresholdPercentage: 40
      - CommandName: "openbanking_savings_savings_get_opening_balance"
        TemplateName: "Q5"
        OverrideTemplateConfig:
          ErrorThresholdPercentage: 40
      - CommandName: "openbanking_savings_savings_get_balance"
        TemplateName: "Q15"
      - CommandName: "openbanking_upi_upi_validate_address"
        TemplateName: "Q10"
      - CommandName: "namecheck_namecheck_un_name_check"
        TemplateName: "Q5"
      - CommandName: "openbanking_payment_payment_pay_deposit_add_funds"
        TemplateName: "Q0"
      - CommandName: "pan_pan_validate"
        TemplateName: "Q0"
      - CommandName: "openbanking_customer_customer_dedupe_check"
        TemplateName: "Q5"
      - CommandName: "ekyc_ekyc_name_dob_validation_for_ekyc"
        TemplateName: "Q0"
    FRESHDESK:
      - CommandName: "cx_freshdesk_freshdesk_get_ticket_by_ticket_id"
        TemplateName: "Q5"
      - CommandName: "cx_freshdesk_freshdesk_get_all_tickets"
        TemplateName: "Q5"
      - CommandName: "cx_freshdesk_freshdesk_update_ticket"
        TemplateName: "Q5"
      - CommandName: "cx_freshdesk_freshdesk_get_contacts"
        TemplateName: "Q5"
      - CommandName: "cx_freshdesk_freshdesk_create_ticket"
        TemplateName: "Q5"
      - CommandName: "cx_freshdesk_freshdesk_get_agent"
        TemplateName: "Q5"
      - CommandName: "cx_freshdesk_freshdesk_add_private_note_in_ticket"
        TemplateName: "Q5"
      - CommandName: "cx_freshdesk_freshdesk_fetch_ticket_conversations"
        TemplateName: "Q5"
      - CommandName: "cx_freshdesk_freshdesk_update_ticket_raw"
        TemplateName: "Q5"
      - CommandName: "cx_freshdesk_freshdesk_get_ticket_field"
        TemplateName: "Q5"

XMLDigitalSignatureSigner:
  KeyParams:
    EpifiFederalUPIPrivateKey:
      ValidFrom: "07 Nov 24 22:58 +0530"
      ValidTill: "07 Nov 25 22:58 +0530"

# generic downtime handler for vg
# the vendor name is the enum value with config values like IsEnabled, StartTime, EndTime
DowntimeConfig:
  NSDL:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2023-05-01 10:00:00"
        EndTimestamp: "2023-05-02 10:00:00"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  CVLKRA:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: true
        StartTimestamp: "2024-10-02 20:00:00"
        EndTimestamp: "2024-10-02 23:00:00"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  CKYC:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: true
        StartTimestamp: "2024-03-15 22:00:00"
        EndTimestamp: "2024-03-16 00:00:00"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  DIGILOCKER:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2022-12-08 21:00:00"
        EndTimestamp: "2022-12-09 06:00:00"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  MANCH:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: true
        StartTimestamp: "2024-12-15 00:00:00"
        EndTimestamp: "2024-12-15 05:00:00"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  TSS:
    # TODO(Brijesh): Remove downtime after migrating to TSS's cloud service
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: true
        StartTime: "04:00"
        EndTime: "05:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2022-11-06 00:00:00"
        EndTimestamp: "2022-11-06 02:00:00"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  EXPERIAN:
    Downtime:
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2025-02-16 00:00:00"
        EndTimestamp: "2025-02-16 06:00:00"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  CIBIL:
    Downtime:
      TimestampBasedDownTime:
        IsEnable: true
        StartTimestamp: "2025-05-25 02:00:00"
        EndTimestamp: "2025-05-25 05:00:00"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 80
    MemoryPercentageLimit: 80
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

DisputeConfig:
  FiRequesterId: "e9378284-2a1d-11ee-be56-0242ac120002"

VendorAddresses:
  RegisteredAddresses:
    FEDERAL_BANK:
      RegionCode: "IN"
      PostalCode: "682031"
      AdministrativeArea: "KERALA"
      Locality: "KOCHI"
      AddressLines:
        - "EPIFI FEDERAL NEO BANKING"
        - "FEDERAL TOWERS,MARINE DRIVE"

CRM:
  Freshdesk:
    Risk:
      DefaultPageSize: 30
      VerdictEnumToValueMapping:
        VERDICT_UNSPECIFIED: ""
        VERDICT_PASS: "Pass"
        VERDICT_FAIL: "Fail"
      ReviewTypeEnumToValueMapping:
        REVIEW_TYPE_UNSPECIFIED: ""
        REVIEW_TYPE_USER_REVIEW: "User Review"
        REVIEW_TYPE_TRANSACTION_REVIEW: "Transaction Review"
        REVIEW_TYPE_AFU_REVIEW: "Afu Review"
        REVIEW_TYPE_LEA_COMPLAINT_REVIEW: "LEA Complaint Review"
        REVIEW_TYPE_ESCALATION_REVIEW: "Escalation Review"
      PriorityEnumToValueMapping:
        PRIORITY_UNSPECIFIED: 0
        PRIORITY_CRITICAL: 4
        PRIORITY_HIGH: 3
        PRIORITY_MEDIUM: 2
        PRIORITY_LOW: 1
      StatusEnumToValueMapping:
        STATUS_UNSPECIFIED: 0
        STATUS_CREATED: 6
        STATUS_ASSIGNED: 7
        STATUS_IN_REVIEW: 8
        STATUS_IN_QA_REVIEW: 9
        STATUS_DONE: 10
        STATUS_WONT_REVIEW: 11
        STATUS_MANUAL_INTERVENTION: 12
        STATUS_PENDING_USER_INFO: 13
        STATUS_REVIEW_ACTION_IN_PROGRESS: 14
        STATUS_MARKED_FOR_AUTO_ACTION: 15
        STATUS_PENDING_ON_USER: 16
      AgentGroupEnumToGroupIdMapping:
        AGENT_GROUP_UNSPECIFIED: 0
        AGENT_GROUP_USER_OUTBOUND_CALL: 89000104316
        AGENT_GROUP_TRANSACTION_REVIEW: 89000104277
        AGENT_GROUP_USER_REVIEW: 89000104525
        AGENT_GROUP_L1: 89000105241
        AGENT_GROUP_L2: 89000105242
        AGENT_GROUP_QA: 89000105324
        AGENT_GROUP_ESCALATION: 89000094612
        AGENT_GROUP_MULTI_REVIEW: 89000105732
      TicketScopeEnumToValueMapping:
        TICKET_SCOPE_UNSPECIFIED: 0
        GLOBAL_ACCESS: 1
        GROUP_ACCESS: 2
        RESTRICTED_ACCESS: 3

FederalLien:
  ReasonEnumToValueMap:
    REASON_UNSPECIFIED: ""

PGPInMemoryEntityStoreParams:
  InternalEntity:
    - Secret: "prod/pgp/v1/epifi-fed-api"
  ExternalEntity:
    - Secret: "prod/pgp/v1/federal-pgp-pub-key-for-epifi"

RedactedRawRequestLogExceptionList: [
  "https://fortuna-ds.data-prod.epifi.in/income_fetch",
  "https://prod.fe-2bv371kswn.aws.fennel.ai/api/v1/log",
  "https://main.epifi.aws.fennel.ai/api/v1/log",
  "https://main.epifi.aws.fennel.ai/api/v1/extract_features",
  "https://api.transunioncibil.com/consumer/dtc/v4/GetCustomerAssets",
]

RedactedRawResponseLogExceptionList: [
  "https://main.epifi.aws.fennel.ai/api/v1/extract_features"
]

FederalAPICreds:
  REQUEST_SOURCE_UNSPECIFIED:
    Path: "prod/vg-vgpci/vendor-api-secrets-federal-default"
  REQUEST_SOURCE_LOANS:
    Path: "prod/vg-vgpci/vendor-api-secrets-federal-b2c-loans"
  REQUEST_SOURCE_BILLPAY_RECHARGE:
    Path: "prod/vg-vgpci/vendor-api-secrets-federal-default"

Uqudo:
  AccessTokenURL: "https://auth.uqudo.io/api/oauth/token"
  PublicKeyURL: "https://id.uqudo.io/api/.well-known/jwks.json"
  FetchImageURL: "https://id.uqudo.io/api/v1/info/img"
  EmiratesIdOcrUrl: "https://id.uqudo.io/api/v1/scan"
  Secrets:
    Path: "prod/vendorgateway/uqudo"

VideoSdk:
  Secrets:
    Path: "prod/vendorgateway/videosdk"


DCIssuance:
  ChargeType: "DC"
  PartnerId: "EPI466FI"
  ApiName: "ENQUIRY"
