//go:build wireinject
// +build wireinject

package wire

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"

	firebase "firebase.google.com/go"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"github.com/aws/aws-sdk-go-v2/service/sesv2"

	"github.com/google/wire"
	"github.com/sendgrid/sendgrid-go"
	"github.com/slack-go/slack"
	"github.com/slack-go/slack/socketmode"
	"go.uber.org/zap"
	"google.golang.org/api/option"

	"github.com/epifi/be-common/pkg/events"
	rechargeVgPb "github.com/epifi/gamma/api/vendorgateway/recharge"
	"github.com/epifi/gamma/vendorgateway/recharge"
	setuRecharge "github.com/epifi/gamma/vendorgateway/recharge/setu"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	types2 "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	epifiRsa "github.com/epifi/be-common/pkg/crypto/rsa/v2"
	cryptoWire "github.com/epifi/be-common/pkg/crypto/wire"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc/interceptors/ratelimit/keygen"
	dsig "github.com/epifi/be-common/pkg/goxmldsig"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/syncwrapper/request"
	"github.com/epifi/be-common/pkg/vendorapi"
	vendorapiPkg "github.com/epifi/be-common/pkg/vendorapi"
	vendorapiPkgGenConf "github.com/epifi/be-common/pkg/vendorapi/config/genconf"
	billpayVgPb "github.com/epifi/gamma/api/vendorgateway/billpay"
	"github.com/epifi/gamma/vendorgateway/billpay/setu"

	tokenizerPb "github.com/epifi/gamma/api/tokenizer"
	vgAaPb "github.com/epifi/gamma/api/vendorgateway/aa"
	bcPb "github.com/epifi/gamma/api/vendorgateway/bouncycastle"
	vgIdfcPb "github.com/epifi/gamma/api/vendorgateway/idfc"
	vgTieringPb "github.com/epifi/gamma/api/vendorgateway/tiering"
	cvlVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	vendorClient "github.com/epifi/gamma/api/vendors/http"
	"github.com/epifi/gamma/api/vendors/sftp"
	"github.com/epifi/gamma/pkg/connectedaccount"
	pkgTokenStore "github.com/epifi/gamma/pkg/tokenstore"
	vgPciConf "github.com/epifi/gamma/vendorgateway-pci/config"
	"github.com/epifi/gamma/vendorgateway/aa"
	aaIgnosis "github.com/epifi/gamma/vendorgateway/aa/analytics/ignosis"
	"github.com/epifi/gamma/vendorgateway/aml"
	"github.com/epifi/gamma/vendorgateway/appscreening/seon"
	"github.com/epifi/gamma/vendorgateway/billpay"
	"github.com/epifi/gamma/vendorgateway/bouncycastle"
	"github.com/epifi/gamma/vendorgateway/ckyc"
	aclvg "github.com/epifi/gamma/vendorgateway/comms/acl"
	"github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendorgateway/config/genconf"
	"github.com/epifi/gamma/vendorgateway/credit_report"
	tokenstore3 "github.com/epifi/gamma/vendorgateway/credit_report/tokenstore"
	creditcard2 "github.com/epifi/gamma/vendorgateway/creditcard"
	"github.com/epifi/gamma/vendorgateway/creditcard/saven"
	"github.com/epifi/gamma/vendorgateway/crm"
	"github.com/epifi/gamma/vendorgateway/crm/leadsquared"
	curencyInsights "github.com/epifi/gamma/vendorgateway/currencyinsights"
	"github.com/epifi/gamma/vendorgateway/cx/chatbot/livechatfallback/senseforth"
	vgNugget "github.com/epifi/gamma/vendorgateway/cx/chatbot/nugget"
	federalVg "github.com/epifi/gamma/vendorgateway/cx/federal"
	attachment2 "github.com/epifi/gamma/vendorgateway/cx/federal/escalations/attachment"
	"github.com/epifi/gamma/vendorgateway/cx/freshchat"
	"github.com/epifi/gamma/vendorgateway/cx/freshdesk"
	"github.com/epifi/gamma/vendorgateway/cx/freshdesk/ticket/attachment"
	cxInHouse "github.com/epifi/gamma/vendorgateway/cx/inhouse"
	"github.com/epifi/gamma/vendorgateway/cx/ozonetel"
	"github.com/epifi/gamma/vendorgateway/cx/solutions"
	digilockerPerfios "github.com/epifi/gamma/vendorgateway/digilocker"
	"github.com/epifi/gamma/vendorgateway/dl"
	"github.com/epifi/gamma/vendorgateway/docs"
	"github.com/epifi/gamma/vendorgateway/ekyc"
	"github.com/epifi/gamma/vendorgateway/email"
	sendgrid2 "github.com/epifi/gamma/vendorgateway/email/sendgrid"
	ses2 "github.com/epifi/gamma/vendorgateway/email/ses"
	"github.com/epifi/gamma/vendorgateway/employment"
	"github.com/epifi/gamma/vendorgateway/esign"
	"github.com/epifi/gamma/vendorgateway/extvalidate"
	"github.com/epifi/gamma/vendorgateway/fcm"
	"github.com/epifi/gamma/vendorgateway/fennel"
	"github.com/epifi/gamma/vendorgateway/fittt"
	"github.com/epifi/gamma/vendorgateway/fittt/roanuz/accesstoken"
	"github.com/epifi/gamma/vendorgateway/gplace"
	"github.com/epifi/gamma/vendorgateway/healthinsurance/riskcovry"
	vgIdfc "github.com/epifi/gamma/vendorgateway/idfc"
	"github.com/epifi/gamma/vendorgateway/idvalidate"
	"github.com/epifi/gamma/vendorgateway/incomeestimator"
	vgRatelimiter "github.com/epifi/gamma/vendorgateway/interceptor/ratelimiter"
	vgRatelimiterNamespace "github.com/epifi/gamma/vendorgateway/interceptor/ratelimiter/namespace"
	"github.com/epifi/gamma/vendorgateway/investments/p2p"
	"github.com/epifi/gamma/vendorgateway/iplocation"
	"github.com/epifi/gamma/vendorgateway/itr"
	"github.com/epifi/gamma/vendorgateway/kyc/uqudo"
	vgIdfcVkyc "github.com/epifi/gamma/vendorgateway/kyc/vkyc/idfc"
	breVg "github.com/epifi/gamma/vendorgateway/lending/bre"
	lamf "github.com/epifi/gamma/vendorgateway/lending/collateral/mutualfund"
	vgCredgenicsPb "github.com/epifi/gamma/vendorgateway/lending/collections/credgenics"
	"github.com/epifi/gamma/vendorgateway/lending/creditcard"
	"github.com/epifi/gamma/vendorgateway/lending/creditline"
	vgDigitapPb "github.com/epifi/gamma/vendorgateway/lending/digitap"
	"github.com/epifi/gamma/vendorgateway/lending/lms/finflux"
	finfluxAuth "github.com/epifi/gamma/vendorgateway/lending/lms/finflux/auth"
	federalPl "github.com/epifi/gamma/vendorgateway/lending/preapprovedloan"
	"github.com/epifi/gamma/vendorgateway/lending/preapprovedloan/abfl"
	idfcPl "github.com/epifi/gamma/vendorgateway/lending/preapprovedloan/idfc"
	lendenPl "github.com/epifi/gamma/vendorgateway/lending/preapprovedloan/lenden"
	"github.com/epifi/gamma/vendorgateway/lending/preapprovedloan/lentra"
	liquiloansPl "github.com/epifi/gamma/vendorgateway/lending/preapprovedloan/liquiloans"
	"github.com/epifi/gamma/vendorgateway/lending/preapprovedloan/moneyview"
	authtoken2 "github.com/epifi/gamma/vendorgateway/lending/preapprovedloan/tokenstore"
	"github.com/epifi/gamma/vendorgateway/lending/securedloans/fiftyfin"
	setuPl "github.com/epifi/gamma/vendorgateway/lending/setu"
	"github.com/epifi/gamma/vendorgateway/liveness"
	"github.com/epifi/gamma/vendorgateway/location"
	"github.com/epifi/gamma/vendorgateway/merchantresolution"
	"github.com/epifi/gamma/vendorgateway/moengage"
	"github.com/epifi/gamma/vendorgateway/namecheck"
	employernamecategoriser "github.com/epifi/gamma/vendorgateway/namecheck/employernamecategoriser"
	"github.com/epifi/gamma/vendorgateway/namecheck/employernamematch"
	"github.com/epifi/gamma/vendorgateway/ocr"
	"github.com/epifi/gamma/vendorgateway/offers/dreamfolks"
	"github.com/epifi/gamma/vendorgateway/offers/loylty"
	"github.com/epifi/gamma/vendorgateway/offers/poshvine"
	"github.com/epifi/gamma/vendorgateway/offers/qwikcilver"
	"github.com/epifi/gamma/vendorgateway/offers/qwikcilver/tokenstore"
	"github.com/epifi/gamma/vendorgateway/offers/thriwe"
	"github.com/epifi/gamma/vendorgateway/offers/vistara"
	"github.com/epifi/gamma/vendorgateway/onsurity"
	"github.com/epifi/gamma/vendorgateway/openbanking/accounts"
	"github.com/epifi/gamma/vendorgateway/openbanking/auth"
	"github.com/epifi/gamma/vendorgateway/openbanking/auth/partnersdk"
	"github.com/epifi/gamma/vendorgateway/openbanking/bank_customer"
	"github.com/epifi/gamma/vendorgateway/openbanking/card"
	"github.com/epifi/gamma/vendorgateway/openbanking/customer"
	"github.com/epifi/gamma/vendorgateway/openbanking/deposit"
	"github.com/epifi/gamma/vendorgateway/openbanking/dispute"
	"github.com/epifi/gamma/vendorgateway/openbanking/enach"
	"github.com/epifi/gamma/vendorgateway/openbanking/lien"
	"github.com/epifi/gamma/vendorgateway/openbanking/payment"
	"github.com/epifi/gamma/vendorgateway/openbanking/payment/b2c"
	internationalfundtransferPb "github.com/epifi/gamma/vendorgateway/openbanking/payment/internationalfundtransfer"
	"github.com/epifi/gamma/vendorgateway/openbanking/savings"
	"github.com/epifi/gamma/vendorgateway/openbanking/shipping_preference"
	"github.com/epifi/gamma/vendorgateway/openbanking/standinginstruction"
	"github.com/epifi/gamma/vendorgateway/openbanking/upi"
	"github.com/epifi/gamma/vendorgateway/pan"
	"github.com/epifi/gamma/vendorgateway/parser"
	pg "github.com/epifi/gamma/vendorgateway/payment_gateway"
	"github.com/epifi/gamma/vendorgateway/phonenetwork"
	profileValidationPb "github.com/epifi/gamma/vendorgateway/profilevalidation"
	"github.com/epifi/gamma/vendorgateway/risk"
	"github.com/epifi/gamma/vendorgateway/scienaptic"
	"github.com/epifi/gamma/vendorgateway/shipway"
	"github.com/epifi/gamma/vendorgateway/slack_bot"
	"github.com/epifi/gamma/vendorgateway/sms"
	"github.com/epifi/gamma/vendorgateway/stocks"
	"github.com/epifi/gamma/vendorgateway/stocks/catalog"
	"github.com/epifi/gamma/vendorgateway/stocks/catalog/bridgewise"
	"github.com/epifi/gamma/vendorgateway/stocks/catalog/nps"
	tokenstore2 "github.com/epifi/gamma/vendorgateway/stocks/tokenstore"
	"github.com/epifi/gamma/vendorgateway/tiering"
	"github.com/epifi/gamma/vendorgateway/transactionmonitoring"
	"github.com/epifi/gamma/vendorgateway/userseg"
	federalv2 "github.com/epifi/gamma/vendorgateway/vendorapi/cryptor/federal/v2"
	"github.com/epifi/gamma/vendorgateway/vendorsse"
	"github.com/epifi/gamma/vendorgateway/vendorws"
	"github.com/epifi/gamma/vendorgateway/vkyc"
	"github.com/epifi/gamma/vendorgateway/vkyc/karza/authtoken"
	"github.com/epifi/gamma/vendorgateway/vkyccall"
	wealthCkyc "github.com/epifi/gamma/vendorgateway/wealth/ckyc"
	wealthDigilocker "github.com/epifi/gamma/vendorgateway/wealth/digilocker"
	wealthDigio "github.com/epifi/gamma/vendorgateway/wealth/digio"
	wealthOcr "github.com/epifi/gamma/vendorgateway/wealth/inhouseocr"
	wealthKarza "github.com/epifi/gamma/vendorgateway/wealth/karza"
	"github.com/epifi/gamma/vendorgateway/wealth/kra"
	wealthManch "github.com/epifi/gamma/vendorgateway/wealth/manch"
	"github.com/epifi/gamma/vendorgateway/wealth/mutualfund"
	mfAnalytics "github.com/epifi/gamma/vendorgateway/wealth/mutualfund/analytics"
	"github.com/epifi/gamma/vendorgateway/wealth/mutualfund/holdingsimporter"
	"github.com/epifi/gamma/vendorgateway/wealth/mutualfund/morningstar"
	"github.com/epifi/gamma/vendorgateway/wealth/mutualfund/token/mf_central"
	"github.com/epifi/gamma/vendorgateway/wealth/nsdl"
	wealthTokenStore "github.com/epifi/gamma/vendorgateway/wealth/tokenstore"
	"github.com/epifi/gamma/vendorgateway/whatsapp"
	"github.com/epifi/gamma/vendorgateway/wire/providers"
	"github.com/epifi/gamma/vendorgateway/wire/types"
	"github.com/epifi/gamma/vendorgateway/zenduty"
)

// Returns the CAs used by the vendors
func getRootCAs(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) (rootCAs []string) {

	// Federal CAs for remittance.federalbank.co.in
	// Used for UPI APIs
	if remittanceFederalRootCa, ok := gconf.Secrets().Ids[config.RemittanceFederalRootCa]; ok {
		rootCAs = append(rootCAs, remittanceFederalRootCa)
	}
	if remittanceFederalRootCaV1, ok := gconf.Secrets().Ids[config.RemittanceFederalRootCaV1]; ok {
		rootCAs = append(rootCAs, remittanceFederalRootCaV1)
	}
	if remittanceFederalRootCaV2, ok := gconf.Secrets().Ids[config.RemittanceFederalRootCaV2]; ok {
		rootCAs = append(rootCAs, remittanceFederalRootCaV2)
	}
	// Adding intermediate CA also to be on the safer side
	if remittanceFederalIntermediateCa, ok := gconf.Secrets().Ids[config.RemittanceFederalIntermediateCa]; ok {
		rootCAs = append(rootCAs, remittanceFederalIntermediateCa)
	}
	if remittanceFederalIntermediateCaV1, ok := gconf.Secrets().Ids[config.RemittanceFederalIntermediateCaV1]; ok {
		rootCAs = append(rootCAs, remittanceFederalIntermediateCaV1)
	}
	if remittanceFederalIntermediateCaV2, ok := gconf.Secrets().Ids[config.RemittanceFederalIntermediateCaV2]; ok {
		rootCAs = append(rootCAs, remittanceFederalIntermediateCaV2)
	}
	return
}

func nilSigningContextProvider() *dsig.SigningContext {
	return nil
}

func getS3Downloader(ctx context.Context, conf *config.Config) *manager.Downloader {
	awsConf, err := awsconfpkg.NewAWSConfig(ctx, conf.Aws.Region, true)
	if err != nil {
		logger.Fatal("failed to initialise AWS config", zap.Error(err))
	}
	return manager.NewDownloader(s3.NewFromConfig(awsConf))
}

func getS3Uploader(ctx context.Context, conf *config.Config) *manager.Uploader {
	awsConf, err := awsconfpkg.NewAWSConfig(ctx, conf.Aws.Region, true)
	if err != nil {
		logger.Fatal("failed to initialise AWS config", zap.Error(err))
	}
	return manager.NewUploader(s3.NewFromConfig(awsConf))
}

var SecureHttpClientNilSignCtxWireSet = wire.NewSet(providers.SecureHttpClientProvider, nilSigningContextProvider)

func InitializeSyncWrapperConsumerService() *vendorapiPkg.SyncWrapperConsumer {
	wire.Build(
		vendorapiPkg.NewSyncWrapperConsumer,
	)
	return &vendorapiPkg.SyncWrapperConsumer{}
}

func InitializePartnerSDKService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *partnersdk.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		partnersdk.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &partnersdk.Service{}
}

func InitializeStandingInstructionService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *standinginstruction.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		standinginstruction.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &standinginstruction.Service{}
}

func emptyHttpClientProvider() *http.Client {
	return &http.Client{}
}

func InitializeDummySMSService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *sms.Service {
	wire.Build(
		envProvider,
		emptyHttpClientProvider,
		nilSigningContextProvider,
		sms.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &sms.Service{}
}

func InitializeSMSService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *sms.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForCommsVendors,
		nilSigningContextProvider,
		sms.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &sms.Service{}
}

func InitializeGPlaceService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *gplace.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		gplace.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &gplace.Service{}
}

func InitializeCustomerService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *customer.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		datetime.WireDefaultTimeSet,
		customer.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &customer.Service{}
}

func InitializeSavingsService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *savings.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		savings.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &savings.Service{}
}

func InitializeShippingPreferenceService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *shipping_preference.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		shipping_preference.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &shipping_preference.Service{}
}

func InitializeVendorAuthService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *auth.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		auth.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)

	return &auth.Service{}
}

func InitializeBankCustomerService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *bank_customer.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		bank_customer.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,

		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &bank_customer.Service{}
}

func InitializePaymentService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *payment.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		payment.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &payment.Service{}
}

func InitializeCKYCService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *ckyc.Service {

	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		ckyc.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &ckyc.Service{}
}

func InitializeEKYCService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *ekyc.Service {

	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		ekyc.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &ekyc.Service{}
}

func InitializeDOCSService(conf *config.Config, gconf *genconf.Config, vendorapiGConf *vendorapiPkgGenConf.Config) *docs.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		docs.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &docs.Service{}
}

func InitializeLivenessService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *liveness.Service {
	wire.Build(
		envProvider,
		vendorClient.NewKarzaHttpClient,
		nilSigningContextProvider,
		liveness.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &liveness.Service{}
}

func InitializeEmploymentService(redisClient types2.VendorgatewayRedisStore, conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *employment.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForEmploymentService,
		providers.GetKarzaEPFPassbookHttpClient,
		nilSigningContextProvider,
		types2.VendorgatewayRedisStoreRedisClientProvider,
		employment.NewService,
		vendorapiPkg.New,
		GetKarzaGetPassbookHttpRequestHandler,
		wealthTokenStore.RedisTokenStoreWireSet,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &employment.Service{}
}

func InitializeTransactionMonitoringService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *transactionmonitoring.Service {
	wire.Build(
		envProvider,
		providers.GetDronaPayHttpClient,
		nilSigningContextProvider,
		transactionmonitoring.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &transactionmonitoring.Service{}
}

func nilVgPciConfigProvider() *vgPciConf.Config {
	return nil
}

func InitializeCardProvisioningService(ctx context.Context, vgConfig *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config, tokenizerClient tokenizerPb.TokenizerClient) (*card.Service, error) {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		nilVgPciConfigProvider,
		card.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		card.NewFactory,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		InitCryptors,
	)
	return &card.Service{}, nil
}

func InitializeUqudoService(gconf *genconf.Config, vendorApiGConf *vendorapiPkgGenConf.Config) *uqudo.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		uqudo.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &uqudo.Service{}
}

func InitializePANService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) (*pan.Service, error) {
	wire.Build(
		envProvider,
		getSftpClientForEpanFederal,
		providers.GetPANServiceHttpClient,
		nilSigningContextProvider,
		pan.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &pan.Service{}, nil
}

func InitializeUNNameCheckService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *namecheck.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		namecheck.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &namecheck.Service{}
}

func InitializeEmployerNameMatchService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *employernamematch.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		employernamematch.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &employernamematch.Service{}
}

func InitializeEmployerNameCategoriserService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *employernamecategoriser.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		employernamecategoriser.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &employernamecategoriser.Service{}
}

func InitializeSenseforthLiveChatFallbackService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *senseforth.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForCx,
		nilSigningContextProvider,
		senseforth.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &senseforth.Service{}
}

func InitializeFreshdeskService(ctx context.Context, conf *config.Config, genConf *genconf.Config, config2 *vendorapiPkgGenConf.Config) *freshdesk.Service {

	wire.Build(
		envProvider,
		providers.GetHttpClientForCx,
		nilSigningContextProvider,
		getS3Downloader,
		freshdesk.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		wire.NewSet(attachment.NewTicketAttachments, wire.Bind(new(attachment.AttachmentsManager), new(*attachment.TicketAttachments))),
	)
	return &freshdesk.Service{}
}

func InitializeCRMService(conf *config.Config, redisClient types2.VendorgatewayRedisStore, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *crm.Service {

	wire.Build(
		envProvider,
		providers.GetHttpClientForCrm,
		nilSigningContextProvider,
		types2.VendorgatewayRedisStoreRedisClientProvider,
		crm.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
	)
	return &crm.Service{}
}

func InitializeFreshChatServer(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *freshchat.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForCx,
		nilSigningContextProvider,
		freshchat.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &freshchat.Service{}
}

func InitializeOzonetelService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *ozonetel.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForCx,
		nilSigningContextProvider,
		ozonetel.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &ozonetel.Service{}
}

func InitializeSolutionsService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *solutions.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForCx,
		nilSigningContextProvider,
		solutions.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &solutions.Service{}
}

func InitializeDepositService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *deposit.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		deposit.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &deposit.Service{}
}

func InitializeB2CService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *b2c.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		b2c.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &b2c.Service{}
}

func InitializeLeadSquaredService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *leadsquared.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		leadsquared.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &leadsquared.Service{}
}

func InitializeLoyltyService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *loylty.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForLoylty,
		nilSigningContextProvider,
		loylty.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		loylty.InMemoryCacheStoreWireSet,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &loylty.Service{}
}

func qwikcliverConfProvider(conf *config.Config) *config.Qwikcilver {
	return conf.Application.Qwikcilver
}
func InitializeQwikcilverService(redisClient types2.VendorgatewayRedisStore, conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *qwikcilver.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForQwikcilver,
		qwikcliverConfProvider,
		nilSigningContextProvider,
		types2.VendorgatewayRedisStoreRedisClientProvider,
		lock.RedisV9LockManagerWireSet,
		tokenstore.RedisTokenStoreWireSet,
		qwikcilver.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &qwikcilver.Service{}
}

func InitializeVkycService(redisClient types2.VendorgatewayRedisStore, conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *vkyc.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		types2.VendorgatewayRedisStoreRedisClientProvider,
		httpcontentredactor.GetInstance,
		vkyc.NewService,
		vendorapiPkg.New,
		authtoken.InMemoryStoreWireSet,
		lock.DefaultLockMangerWireSet,
		cache.RedisStorageWireSet,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &vkyc.Service{}
}

func InitialiseFitttCricketServcie(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *fittt.Service {
	wire.Build(
		envProvider,
		providers.InsecureHttpClientProvider,
		nilSigningContextProvider,
		httpcontentredactor.GetInstance,
		fittt.NewService,
		vendorapiPkg.New,
		accesstoken.InMemoryStoreWireSet,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &fittt.Service{}
}

func envProvider(gconf *genconf.Config) string {
	return gconf.Application().Environment()
}

func InitializeAccountsServer(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *accounts.Service {
	wire.Build(
		SecureHttpClientNilSignCtxWireSet,
		envProvider,
		accounts.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &accounts.Service{}
}

func InitializeWhatsAppService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *whatsapp.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForWhatsapp,
		nilSigningContextProvider,
		whatsapp.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &whatsapp.Service{}
}

func InitializeIpLocationService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *iplocation.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		iplocation.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &iplocation.Service{}
}

func InitializeAAService(aggregatorClient vgAaPb.AccountAggregatorClient, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *aa.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForAA,
		nilSigningContextProvider,
		connectedaccount.InMemoryAaCacheWireSet,
		aa.NewService,
		vendorapiPkg.New,
		aa.NewFactory,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &aa.Service{}
}

func InitializeBcService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *bouncycastle.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForAA,
		nilSigningContextProvider,
		bouncycastle.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &bouncycastle.Service{}
}

func getSftpClientForCvlDownload(conf *config.Config) (kra.DownloadISftp, error) {
	sftpClientConfig, err := sftp.NewClientConfig(conf.Secrets.Ids[config.CvlSftpDownloadUser], conf.Secrets.Ids[config.CvlSftpDownloadPass], conf.Application.CvlKra.Host, conf.Application.CvlKra.Port, conf.Secrets.Ids[config.CvlSftpSshKey])
	if err != nil {
		return nil, err
	}
	return sftp.NewSftp(sftpClientConfig), err
}

func getSftpClientForCvlUpload(conf *config.Config) (kra.UploadISftp, error) {
	sftpClientConfig, err := sftp.NewClientConfig(conf.Application.CvlSecrets.GetSftpUploadUser(), conf.Application.CvlSecrets.GetSftpUploadPass(), conf.Application.CvlKra.Host, conf.Application.CvlKra.Port, conf.Secrets.Ids[config.CvlSftpSshKey])
	if err != nil {
		return nil, err
	}
	return sftp.NewSftp(sftpClientConfig), err
}

func InitializeWealthCvlService(conf *config.Config, wealthCvlClient cvlVgPb.CvlClient, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) (*kra.Service, error) {
	wire.Build(
		envProvider,
		getSftpClientForCvlDownload,
		getSftpClientForCvlUpload,
		providers.GetHttpClientForWealth,
		nilSigningContextProvider,
		kra.NewCvlKraFactory,
		kra.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		wire.NewSet(cache.NewInMemoryCacheService, wire.Bind(new(cache.CacheStorage), new(*cache.InMemoryCacheService))),
	)
	return &kra.Service{}, nil
}

func InitializeWealthNsdlService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *nsdl.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForWealth,
		nilSigningContextProvider,
		nsdl.NewNsdlFactory,
		nsdl.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &nsdl.Service{}
}

func getS3DownloaderForMutualFund(ctx context.Context, conf *config.Config) *manager.Downloader {
	awsConf, err := awsconfpkg.NewAWSConfig(ctx, conf.Aws.Region, true)
	if err != nil {
		logger.Fatal("failed to initialise AWS config", zap.Error(err))
	}
	/*
		S3ForcePathStyle needs to be set to true inorder for local stack to work :
																		https://github.com/aws/aws-sdk-go/issues/2743
		ToDo: Figure of if there is any problem if we set this to true in production. If so, then set it to true only
			  for the development environment.
	*/

	s3Downloader := manager.NewDownloader(s3.NewFromConfig(awsConf, func(options *s3.Options) {
		options.UsePathStyle = true
	}))

	return s3Downloader
}

func InitializeWealthMutualFundService(ctx context.Context, conf *config.Config, redisClient types2.VendorgatewayRedisStore, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *mutualfund.Service {
	wire.Build(
		envProvider,
		providers.GetInsecureHttpClientForWealth,
		nilSigningContextProvider,
		getS3DownloaderForMutualFund,
		types2.VendorgatewayRedisStoreRedisClientProvider,
		mutualfund.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		mutualfund.NewMutualFundFactory,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
		wealthTokenStore.RedisTokenStoreWireSet,
		morningstar.MorningStarAccessTokenFetcherWireSet,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &mutualfund.Service{}
}

func InitializeCreditReportService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *credit_report.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForExperian,
		GetCibilHttpRequestHandler,
		httpcontentredactor.GetInstance,
		nilSigningContextProvider,
		vendorapiPkg.New,
		credit_report.NewService,
		tokenstore3.CreditReportTokenManagerWireSet,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		wire.NewSet(cache.NewInMemoryCacheService, wire.Bind(new(cache.CacheStorage), new(*cache.InMemoryCacheService))),
	)
	return &credit_report.Service{}
}

func InitialiseShipwayService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *shipway.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForShipway,
		nilSigningContextProvider,
		shipway.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &shipway.Service{}
}

func InitializeWealthCkycService(conf *config.Config, bcClient bcPb.BouncyCastleClient, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *wealthCkyc.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForWealth,
		nilSigningContextProvider,
		wire.NewSet(wealthCkyc.NewCkycCryptor, wire.Bind(new(crypto.Cryptor), new(*wealthCkyc.CkycCryptor))),
		wealthCkyc.NewSecureExchange,
		wealthCkyc.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &wealthCkyc.Service{}
}

func InitializeWealthManchService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *wealthManch.Service {
	wire.Build(
		envProvider,
		providers.GetInsecureHttpClientForWealth,
		nilSigningContextProvider,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wealthManch.NewService,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &wealthManch.Service{}
}
func InitializeWealthKarzaService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *wealthKarza.Service {
	wire.Build(
		envProvider,
		providers.GetInsecureHttpClientForWealth,
		nilSigningContextProvider,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wealthKarza.NewService,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &wealthKarza.Service{}
}

func InitializeWealthDigioService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *wealthDigio.Service {
	wire.Build(
		envProvider,
		providers.GetInsecureHttpClientForWealth,
		nilSigningContextProvider,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wealthDigio.NewService,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &wealthDigio.Service{}
}

func InitializeDlAuthService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *dl.Service {
	wire.Build(
		envProvider,
		providers.GetKarzaHttpClient,
		nilSigningContextProvider,
		dl.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &dl.Service{}
}

func InitializeParserService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *parser.Service {
	wire.Build(
		envProvider,
		providers.GetVendorHttpClient,
		nilSigningContextProvider,
		parser.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &parser.Service{}
}

func InitializeSeonService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *seon.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForSeon,
		nilSigningContextProvider,
		seon.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &seon.Service{}
}

func InitializeIdValidateService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *idvalidate.Service {
	wire.Build(
		envProvider,
		providers.GetKarzaHttpClient,
		nilSigningContextProvider,
		idvalidate.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &idvalidate.Service{}
}

func InitializeWealthInhouseOCRService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *wealthOcr.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForWealth,
		nilSigningContextProvider,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wealthOcr.NewService,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &wealthOcr.Service{}
}

func InitializeCXInHouseService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *cxInHouse.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForCx,
		nilSigningContextProvider,
		cxInHouse.NewCXInHouseService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &cxInHouse.Service{}
}

func InitializeWealthDigilockerService(redisClient types2.VendorgatewayRedisStore, conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *wealthDigilocker.Service {
	wire.Build(
		envProvider,
		providers.GetHttpClientForWealth,
		nilSigningContextProvider,
		types2.VendorgatewayRedisStoreRedisClientProvider,
		vendorapiPkg.New,
		wealthTokenStore.RedisTokenStoreWireSet,
		httpcontentredactor.GetInstance,
		wealthDigilocker.NewService,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &wealthDigilocker.Service{}
}

func getSftpClientForP2PInvestmentDataDownload(conf *config.Config) (sftp.ISftp, error) {
	sftpClientConfig, err := sftp.NewInsecureClientConfigWithTimeout(conf.Secrets.Ids[config.P2PInvestmentLiquiloansSftpUser], conf.Secrets.Ids[config.P2PInvestmentLiquiloansSftpPassword],
		conf.Application.Liquiloans.SftpHost, conf.Application.Liquiloans.SftpPort, conf.Application.Liquiloans.SftpTimeoutInSeconds)
	if err != nil {
		return nil, err
	}
	return sftp.NewSftp(sftpClientConfig), err
}

func InitializeP2pInvestmentService(ctx context.Context, conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) (*p2p.Service, error) {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		getSftpClientForP2PInvestmentDataDownload,
		getS3Downloader,
		getS3Uploader,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		p2p.NewService,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &p2p.Service{}, nil
}

func InitializeLocationService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *location.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		location.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &location.Service{}
}

func InitializeExternalValidateService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *extvalidate.Service {
	wire.Build(
		envProvider,
		providers.GetKarzaHttpClient,
		nilSigningContextProvider,
		extvalidate.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &extvalidate.Service{}
}

func InitializeUserSegmentationService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *userseg.Service {
	wire.Build(
		envProvider,
		providers.GetVendorHttpClient,
		nilSigningContextProvider,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		userseg.NewService,
	)
	return &userseg.Service{}
}

func InitialisePhoneNetworkService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *phonenetwork.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		phonenetwork.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &phonenetwork.Service{}
}

func InitialiseCredgenicsService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *vgCredgenicsPb.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		vgCredgenicsPb.NewService,
		vendorapiPkg.New,
		authtoken2.InMemoryStoreWireSet,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &vgCredgenicsPb.Service{}
}

func InitialiseFinfluxService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config, redisClient types2.VendorgatewayRedisStore) *finflux.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		finflux.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		finfluxAuth.WireSet,
		cache.RedisStorageWireSet,
		types2.VendorgatewayRedisStoreRedisClientProvider,
		lock.RedisV9LockManagerWireSet,
	)
	return &finflux.Service{}
}

func getSftpClientForPreApprovedLoanFederalUpload(conf *genconf.Config) (sftp.ISftp, error) {
	sftpClientConfig, err := sftp.NewClientConfig(conf.Application().Lending().PreApprovedLoan().Federal().UserNameSftp, conf.Application().Lending().PreApprovedLoan().Federal().PasswordSftp, conf.Application().Lending().PreApprovedLoan().Federal().SftpHost, conf.Application().Lending().PreApprovedLoan().Federal().SftpPort, conf.Secrets().Ids[config.FederalSftpSshKey])
	if err != nil {
		return nil, err
	}
	return sftp.NewSftp(sftpClientConfig), err
}

func InitialisePreApprovedLoanService(ctx context.Context, conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) (*federalPl.Service, error) {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		federalPl.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		InitCryptors,
	)
	return &federalPl.Service{}, nil
}

func staticPreapprovedLoanConfigProvider(conf *config.Config) *config.PreApprovedLoan {
	return conf.Application.Lending.PreApprovedLoan
}

func preapprovedLoanConfigProvider(conf *genconf.Config) *genconf.PreApprovedLoan {
	return conf.Application().Lending().PreApprovedLoan()
}

func lendenConfigProvider(conf *genconf.Config) *config.Lenden {
	return conf.Application().Lending().PreApprovedLoan().Lenden()
}

func moneyviewConfigProvider(conf *genconf.Config) *config.Moneyview {
	return conf.Application().Lending().PreApprovedLoan().Moneyview()
}

func digitapConfigProvider(conf *genconf.Config) *config.Digitap {
	return conf.Application().Lending().PreApprovedLoan().Digitap()
}

func setuConfigProvider(conf *genconf.Config) *config.Setu {
	return conf.Application().Lending().PreApprovedLoan().Setu()
}

func InitialiseLiquiloansService(config *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *liquiloansPl.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		staticPreapprovedLoanConfigProvider,
		liquiloansPl.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &liquiloansPl.Service{}
}

func InitialiseSetuService(config *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *setuPl.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		setuConfigProvider,
		setuPl.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &setuPl.Service{}
}

func InitialiseMoneyviewService(config2 *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *moneyview.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		moneyviewConfigProvider,
		moneyview.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		wire.NewSet(cache.NewInMemoryCacheService, wire.Bind(new(cache.CacheStorage), new(*cache.InMemoryCacheService))),
	)
	return &moneyview.Service{}
}

func InitialiseIdfcService(config *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *idfcPl.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		preapprovedLoanConfigProvider,
		idfcPl.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		authtoken2.InMemoryStoreWireSet,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &idfcPl.Service{}
}

func InitialiseLendenService(config *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *lendenPl.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		lendenConfigProvider,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		lendenPl.NewService,
	)
	return &lendenPl.Service{}
}

func securedLoansConfigProvider(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *config.SecuredLoans {
	return gconf.Application().Lending().SecuredLoans()
}

func InitialiseFiftyFinService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *fiftyfin.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		securedLoansConfigProvider,
		fiftyfin.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &fiftyfin.Service{}
}

func InitialiseEsignService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *esign.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		esign.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &esign.Service{}
}

func lendingMfCentralConfigProvider(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *config.LendingMFCentralConfig {
	return gconf.Application().Lending().Collateral().LendingMFCentralConfig
}

func InitialiseLamfService(redisClient types2.VendorgatewayRedisStore, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *lamf.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		types2.VendorgatewayRedisStoreRedisClientProvider,
		cache.RedisStorageWireSet,
		lendingMfCentralConfigProvider,
		lamf.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &lamf.Service{}
}

func InitialiseInhouseRiskService(dynConf *genconf.Config, config2 *vendorapiPkgGenConf.Config) *risk.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		risk.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &risk.Service{}
}

func InitialiseBreService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *breVg.Service {
	wire.Build(
		envProvider,
		providers.InsecureHttpClientProvider,
		nilSigningContextProvider,
		breVg.NewBusinessRuleEngineService,
		httpcontentredactor.GetInstance,
		vendorapiPkg.New,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &breVg.Service{}
}

func InitialiseCreditCardService(c *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *creditcard.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		nilVgPciConfigProvider,
		creditcard.NewCreditCardService,
		httpcontentredactor.GetInstance,
		vendorapiPkg.New,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &creditcard.Service{}
}

func InitializeCurrencyInsightsService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *curencyInsights.Service {
	wire.Build(
		envProvider,
		nilSigningContextProvider,
		curencyInsights.NewCurrencyInsightFactory,
		providers.GetHttpClientForVisa,
		curencyInsights.NewService,
		httpcontentredactor.GetInstance,
		vendorapiPkg.New,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &curencyInsights.Service{}
}

func InitialiseCreditLineService(c *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *creditline.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		creditline.NewCreditlineService,
		httpcontentredactor.GetInstance,
		vendorapiPkg.New,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &creditline.Service{}
}

func InitialiseUSStockService(c *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *stocks.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		vendorapiPkg.New,
		vendorws.NewConnectionHandler,
		vendorsse.NewConnectionHandler,
		httpcontentredactor.GetInstance,
		stocks.NewService,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &stocks.Service{}
}

func InitialiseVgKeyGenerator() keygen.IKeyGenerator {
	wire.Build(
		vgRatelimiter.NewVgKeyGenerator,
		vgRatelimiterNamespace.NewDefaultFactory,
		vgRatelimiterNamespace.NewInitiateB2CPayNamespaceGenerator,
	)
	return &vgRatelimiter.VgKeyGenerator{}
}

func InitialiseProfileValidationService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *profileValidationPb.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		profileValidationPb.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &profileValidationPb.Service{}
}

func InitializeAmlService(conf *config.Config, confs *genconf.Config, config2 *vendorapiPkgGenConf.Config) *aml.Service {
	wire.Build(
		envProvider,
		providers.InsecureHttpClientProvider,
		nilSigningContextProvider,
		aml.NewAmlFactory,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		aml.NewService,
		amlConfProvider,
	)
	return &aml.Service{}
}

func amlConfProvider(conf *genconf.Config) *config.Aml {
	return conf.Application().Aml()
}

func InitializeInternationalfundtransferService(c *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *internationalfundtransferPb.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		internationalfundtransferPb.NewService,
		httpcontentredactor.GetInstance,
		vendorapiPkg.New,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &internationalfundtransferPb.Service{}
}

func InitializeRiskcovryService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *riskcovry.Service {
	wire.Build(
		envProvider,
		providers.GetVendorHttpClient,
		nilSigningContextProvider,
		riskcovry.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &riskcovry.Service{}
}

func InitializeOnsurityService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *onsurity.Service {
	wire.Build(
		envProvider,
		providers.GetVendorHttpClient,
		nilSigningContextProvider,
		onsurity.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapi.HttpDoer), new(*http.Client)),
	)
	return &onsurity.Service{}
}

func verifyAndDecryptUrl(gconf *genconf.Config) types.VerifyAndDecryptUrl {
	return types.VerifyAndDecryptUrl(gconf.Application().MFCentral().VerifyAndDecryptURL)
}

func encryptAndSignUrl(gconf *genconf.Config) types.EncryptAndSignUrl {
	return types.EncryptAndSignUrl(gconf.Application().MFCentral().EncryptAndSignURL)
}

func urlProvider(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) string {
	return gconf.Application().MFCentral().VerifyAndDecryptURL
}

func InitializeHoldingsImporterService(redisClient types2.VendorgatewayRedisStore, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *holdingsimporter.Service {
	wire.Build(
		providers.GetInsecureHttpClientForWealth,
		nilSigningContextProvider,
		verifyAndDecryptUrl,
		encryptAndSignUrl,
		types2.VendorgatewayRedisStoreRedisClientProvider,
		cache.RedisStorageWireSet,
		holdingsimporter.NewService,
		vendorApiPkgProvider,
		httpcontentredactor.GetInstance,
		holdingsimporter.NewHoldingsImporterFactory,
		mf_central.NewVerifyAndDecrypt,
		mf_central.NewEncryptAndSign,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &holdingsimporter.Service{}
}

func vendorApiPkgProvider(cl vendorapiPkg.HttpDoer, xmlSigner *dsig.SigningContext, h *httpcontentredactor.HTTPContentRedactor, vendorapiGconf *vendorapiPkgGenConf.Config, gconf *genconf.Config) *vendorapiPkg.HTTPRequestHandler {
	wire.Build(
		envProvider,
		vendorapiPkg.New,
	)
	return &vendorapiPkg.HTTPRequestHandler{}
}

func InitalizeUsStockCatalogService(c *config.Config, redisClient types2.VendorgatewayRedisStore, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *catalog.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		types2.VendorgatewayRedisStoreRedisClientProvider,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		catalog.NewService,
		tokenstore2.RedisTokenStoreWireSet,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &catalog.Service{}
}

func InitializeMerchantResolutionService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *merchantresolution.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		merchantresolution.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &merchantresolution.Service{}
}

func InitializeMFAnalyticsService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *mfAnalytics.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		mfAnalytics.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		mfAnalytics.NewMFAnalyticsFactory,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &mfAnalytics.Service{}
}

func dreamfolksConfProvider(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *config.Dreamfolks {
	return gconf.Application().Dreamfolks()
}

func InitializeDreamfolksService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *dreamfolks.Service {
	wire.Build(
		envProvider,
		providers.GetVendorHttpClient,
		nilSigningContextProvider,
		dreamfolksConfProvider,
		dreamfolks.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &dreamfolks.Service{}
}

func thriweConfProvider(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *config.Thriwe {
	return gconf.Application().Thriwe()
}

func InitializeThriweService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *thriwe.Service {
	wire.Build(
		envProvider,
		providers.GetVendorHttpClient,
		nilSigningContextProvider,
		thriweConfProvider,
		thriwe.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &thriwe.Service{}
}

func InitializeMoEngageService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *moengage.Service {
	wire.Build(
		envProvider,
		providers.GetVendorHttpClient,
		nilSigningContextProvider,
		moengageConfProvider,
		moengage.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &moengage.Service{}
}

func moengageConfProvider(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *config.MoEngage {
	return gconf.Application().MoEngage()
}

func poshvineConfProvider(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *config.Poshvine {
	return gconf.Application().Poshvine()
}

func InitializePoshvineService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *poshvine.Service {
	wire.Build(
		envProvider,
		providers.GetVendorHttpClient,
		nilSigningContextProvider,
		poshvineConfProvider,
		poshvine.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &poshvine.Service{}
}

func InitializeLienService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *lien.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		lien.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &lien.Service{}
}

func getSftpClientForVistara(conf *config.Config) (sftp.ISftp, error) {
	var (
		sftpClientConfig *sftp.ClientConfig
		err              error
	)

	// Connect to sftp. we're using insecure connection on prod as there's no SSH key shared by Vistara for now.
	sftpClientConfig, err = sftp.NewClientConfigInsecure(conf.Application.Vistara.Sftp.User, conf.Application.Vistara.Sftp.Password, conf.Application.Vistara.Sftp.Host, conf.Application.Vistara.Sftp.Port)
	if err != nil {
		return nil, err
	}

	return sftp.NewSftp(sftpClientConfig), err
}

func InitialiseVistaraService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) (*vistara.Service, error) {
	wire.Build(
		envProvider,
		getSftpClientForVistara,
		SecureHttpClientNilSignCtxWireSet,
		vistara.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &vistara.Service{}, nil
}

func InitializeTieringService(tieringClient vgTieringPb.TieringClient, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *tiering.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		tiering.NewService,
		tiering.NewFactory,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &tiering.Service{}
}

func InitializeSlackBotService(conf *config.Config) *slack_bot.Service {
	wire.Build(
		initSlackBotClient,
		initSocketModeClient,
		slack_bot.NewService,
	)
	return &slack_bot.Service{}
}

func initSlackBotClient(conf *config.Config) *slack.Client {

	if conf.Application.Environment == cfg.DevelopmentEnv || conf.Application.Environment == cfg.StagingEnv || conf.Application.Environment == cfg.ProductionEnv {
		slackTokens := conf.Application.SlackTokens

		client := slack.New(slackTokens.BotToken, slack.OptionDebug(true), slack.OptionAppLevelToken(slackTokens.AppToken))

		return client
	}

	return nil
}

func initSocketModeClient(client *slack.Client) *socketmode.Client {
	if client == nil {
		return nil
	}
	return socketmode.New(
		client,
		socketmode.OptionDebug(true),
		socketmode.OptionLog(log.New(os.Stdout, "socketmode: ", log.Lshortfile|log.LstdFlags)),
	)
}

func InitializeFennelFeatureStoreService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config, conf *config.Config) *fennel.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		fennel.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &fennel.Service{}
}

func InitializeScienapticService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *scienaptic.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		scienaptic.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &scienaptic.Service{}
}

func InitialiseIncomeEstimatorService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *incomeestimator.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		incomeestimator.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &incomeestimator.Service{}
}

func getSftpClientForEnachFederal(conf *config.Config) (sftp.ISftp, error) {
	var enachSftpSecrets config.SftpSecrets
	if err := json.Unmarshal([]byte(conf.Secrets.Ids[config.EpifiFederalEnachSftpSecrets]), &enachSftpSecrets); err != nil {
		return nil, fmt.Errorf("error unmarshalling sftp secrets json to struct, %w", err)
	}
	// federal ONLY supports "diffie-hellman-group-exchange-sha256" as the strongest key exchange algorithm for ssh so explicitly passing it in config.
	sftpClientConfig, err := sftp.NewClientConfigInsecureWithKeyExchangeAlgos(enachSftpSecrets.User, enachSftpSecrets.Password, conf.Application.EnachConfig.FederalConfig.SftpConn.Host, conf.Application.EnachConfig.FederalConfig.SftpConn.Port, []string{"diffie-hellman-group-exchange-sha256"})
	if err != nil {
		return nil, err
	}
	return sftp.NewSftp(sftpClientConfig), nil
}

func getSftpClientForEpanFederal(conf *config.Config) (sftp.ISftp, error) {
	var epanSftpSecrets config.SftpSecrets
	if err := json.Unmarshal([]byte(conf.Secrets.Ids[config.EpifiFederalEpanSftpSecrets]), &epanSftpSecrets); err != nil {
		return nil, fmt.Errorf("error unmarshalling sftp secrets json to struct, %w", err)
	}
	// federal ONLY supports "diffie-hellman-group-exchange-sha256" as the strongest key exchange algorithm for ssh so explicitly passing it in config.
	sftpClientConfig, err := sftp.NewClientConfigInsecureWithKeyExchangeAlgos(epanSftpSecrets.User, epanSftpSecrets.Password, conf.Application.EpanConfig.FederalConfig.SftpConn.Host, conf.Application.EpanConfig.FederalConfig.SftpConn.Port, []string{"diffie-hellman-group-exchange-sha256"})
	if err != nil {
		return nil, err
	}
	return sftp.NewSftp(sftpClientConfig), nil
}

func InitialiseEnachService(
	gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config,
	c *config.Config,
) (*enach.Service, error) {
	wire.Build(
		envProvider,
		getSftpClientForEnachFederal,
		SecureHttpClientNilSignCtxWireSet,
		enach.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &enach.Service{}, nil
}

func idfcConfigProvider(conf *genconf.Config) *genconf.Idfc {
	return conf.Application().Lending().PreApprovedLoan().Idfc()
}

func InitializeIdfcService(config *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *vgIdfc.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		idfcConfigProvider,
		vgIdfc.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		pkgTokenStore.InMemoryTokenStoreWireSet,
	)
	return &vgIdfc.Service{}
}

func InitializeIdfcVkycService(idfcClient vgIdfcPb.IdfcClient, conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *vgIdfcVkyc.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		vgIdfcVkyc.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &vgIdfcVkyc.Service{}
}

func GetKarzaGetPassbookHttpRequestHandler(karzaGetPassbookHttpClient types.KarzaGetPassbookHttpClient, xmlSigner *dsig.SigningContext,
	hTTPContentRedactor *httpcontentredactor.HTTPContentRedactor, conf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) types.KarzaGetPassbookHttpRequestHandler {
	client := types.KarzaGetPassbookHttpClientProvider(karzaGetPassbookHttpClient)
	return vendorapiPkg.New(client, xmlSigner, hTTPContentRedactor, vendorapiGconf, envProvider(conf))
}

func GetCibilHttpRequestHandler(xmlSigner *dsig.SigningContext,
	hTTPContentRedactor *httpcontentredactor.HTTPContentRedactor, conf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) types.CibilHttpRequestHandler {
	env := envProvider(conf)
	client := providers.GetHttpClientForCibil(conf)
	return vendorapiPkg.New(client, xmlSigner, hTTPContentRedactor, vendorapiGconf, env)
}

func InitialiseLentra(config *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *lentra.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		lentra.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		pkgTokenStore.InMemoryTokenStoreWireSet,
	)
	return &lentra.Service{}
}

func disputeSftpConfProvider(conf *config.Config) *config.DisputeSFTP {
	return conf.DisputeSFTP
}

func InitialiseDisputeService(ctx context.Context, conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *dispute.Service {
	wire.Build(
		envProvider,
		providers.GetDisputeServiceHttpClient,
		nilSigningContextProvider,
		disputeSftpConfProvider,
		getS3Downloader,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		dispute.NewService,
	)
	return &dispute.Service{}
}

func getSendGridClient(conf *config.Config) sendgrid2.ISendGridClient {
	env := conf.Application.Environment
	if env == cfg.TestEnv {
		return sendgrid2.NewMockSendGridSender()
	}
	return sendgrid.NewSendClient(conf.Secrets.Ids[config.SendGridAPIKey])
}

func getSesClient(ctx context.Context, conf *config.Config) ses2.ISesClient {
	env := conf.Application.Environment
	if env == cfg.TestEnv {
		return ses2.NewMockSESSender()
	}
	awsConf, err := awsconfpkg.NewAWSConfig(ctx, conf.Aws.Region, true)
	if err != nil {
		logger.Fatal("failed to initialise AWS session", zap.Error(err))
	}
	return sesv2.NewFromConfig(awsConf)
}

func awsSesProvider(conf *config.Config) *config.AwsSes {
	return conf.AwsSes
}

func InitialiseEmailService(ctx context.Context, conf *config.Config) *email.VgEmailService {
	wire.Build(
		getSendGridClient,
		getSesClient,
		awsSesProvider,
		email.NewVgEmailService,
	)
	return &email.VgEmailService{}
}

func getFirebaseMessagingClient(conf *config.Config) fcm.IFcmClient {
	env := conf.Application.Environment
	if env == cfg.TestEnv {
		return fcm.NewMockFcmService()
	}
	opt := option.WithCredentialsJSON([]byte(conf.Secrets.Ids[config.FCMServiceAccountCredJson]))
	app, err := firebase.NewApp(context.Background(), nil, opt)
	if err != nil {
		logger.Fatal("cannot initialize firebase app instance", zap.Error(err))
	}
	// Obtain a messaging.Client from the App.
	msgClient, err := app.Messaging(context.Background())
	if err != nil {
		logger.Fatal("cannot get firebase messaging client instance", zap.Error(err))
	}
	return msgClient
}

func fcmAnalyticsLabelProvider(conf *config.Config) string {
	return conf.FcmAnalyticsLabel
}

func InitialiseFcmService(conf *config.Config, gconf *genconf.Config) *fcm.Service {
	wire.Build(
		getFirebaseMessagingClient,
		fcmAnalyticsLabelProvider,
		fcm.NewFCMService,
	)
	return &fcm.Service{}
}

// returns xml signer to sign an xml request to the vendor
func getXmlSigner(conf *config.Config) (*dsig.SigningContext, error) {
	ksWithValidity, fallbackKs, err := dsig.NewMemoryRSAKeyStoreWithValidity(map[string]*cfg.XMLDigitalSignatureSigningKeyParam{
		conf.Secrets.Ids[config.EpifiFederalUpiPrivateKey]: conf.XMLDigitalSignatureSigner.KeyParams[config.EpifiFederalUpiPrivateKey],
	}, conf.Secrets.Ids[config.EpifiFederalUPIFallbackPrivateKey])
	if err != nil {
		return nil, fmt.Errorf("failed to load xml signer key store: %w", err)
	}

	ctx, err := dsig.NewDefaultSigningCtxFromRsaKeyWithValidity(ksWithValidity, fallbackKs)
	if err != nil {
		return nil, err
	}
	ctx.Canonicalizer = dsig.MakeC14N10RecCanonicalizer()
	ctx.Prefix = ""
	// Note: Please use NewDefaultSigningContext if not using PublicKey key info type
	ctx.KeyInfoType = dsig.PublicKey

	return ctx, nil
}

func syncWrapperTimeoutConfigProvider(conf *config.Config) int {
	return conf.Application.SyncWrapperTimeout
}

func asyncReqIdHandlerProvider(conf *config.Config) (request.AsyncReqIDHandler, error) {
	return request.NewAsyncReqIDHandler()
}

func asyncRequestHandlerProvider(client *http.Client, xmlSigner *dsig.SigningContext, syncWrapperTimeoutInSeconds int, idHandler request.AsyncReqIDHandler, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *vendorapiPkg.AsyncRequestHandler {
	httpContentRedactor := httpcontentredactor.GetInstance()
	httpRequestHandler := vendorapiPkg.New(client, xmlSigner, httpContentRedactor, vendorapiGconf, envProvider(gconf))
	asyncRequestHandler := vendorapiPkg.NewAsyncRequestHandler(httpRequestHandler, syncWrapperTimeoutInSeconds, idHandler, httpContentRedactor)
	return asyncRequestHandler
}

func listKeysAsyncRequestHandlerProvider(client types.UpiListKeysHttpClient, xmlSigner *dsig.SigningContext, syncWrapperTimeoutInSeconds int, idHandler request.AsyncReqIDHandler, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) upi.ListKeyAsyncHandler {
	httpContentRedactor := httpcontentredactor.GetInstance()
	httpRequestHandler := vendorapiPkg.New(client, xmlSigner, httpContentRedactor, vendorapiGconf, envProvider(gconf))
	asyncRequestHandler := vendorapiPkg.NewAsyncRequestHandler(httpRequestHandler, syncWrapperTimeoutInSeconds, idHandler, httpContentRedactor)
	return asyncRequestHandler
}

func InitialiseUpiService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config, eventBroker events.Broker) (*upi.Service, error) {
	wire.Build(
		httpcontentredactor.GetInstance,
		envProvider,
		vendorapiPkg.New,
		providers.SecureHttpClientProvider,
		getXmlSigner,
		syncWrapperTimeoutConfigProvider,
		providers.GetListKeysHttpClient,
		asyncReqIdHandlerProvider,
		asyncRequestHandlerProvider,
		listKeysAsyncRequestHandlerProvider,
		upi.NewService,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &upi.Service{}, nil
}

func InitialiseAbflService(config *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *abfl.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		staticPreapprovedLoanConfigProvider,
		abfl.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		authtoken2.InMemoryStoreWireSet,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &abfl.Service{}
}

func InitializeITRService(config *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *itr.Service {
	wire.Build(
		envProvider,
		providers.GetITRServiceHttpClient,
		nilSigningContextProvider,
		itr.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)))

	return &itr.Service{}
}

func InitialiseOCRService(config *genconf.Config, config2 *vendorapiPkgGenConf.Config) *ocr.Service {
	wire.Build(
		envProvider,
		ocr.NewOCRService,
		providers.SecureHttpClientProvider,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		nilSigningContextProvider,
	)
	return &ocr.Service{}
}

func InitCryptors(ctx context.Context, conf *config.Config) (*cryptormap.InMemoryCryptorStore, error) {
	var (
		rsaCryptor crypto.Cryptor
	)

	awsConfig, err := awsconfpkg.NewAWSConfig(ctx, conf.Aws.Region, true)
	if err != nil {
		return nil, fmt.Errorf("failed to initialise AWS config: %w", err)
	}

	es := cryptoWire.InitializeInMemoryStore(secretsmanager.NewFromConfig(awsConfig))

	err = es.AddInternalPrivateKeyFromSecret(conf.PGPInMemoryEntityStoreParams.InternalEntity)
	if err != nil {
		return nil, err
	}

	err = es.AddExternalPublicKeysFromSecret(conf.PGPInMemoryEntityStoreParams.ExternalEntity)
	if err != nil {
		return nil, err
	}

	pgpCryptor := cryptoWire.InitializePGPCryptor(es)

	if conf.Flags.EnableFederalCardDecryptionByFallbackKey {
		rsaCryptor = epifiRsa.NewDecryptOnlyCryptor([]string{conf.Secrets.Ids[config.EpifiFederalCardDataPrivateKey],
			conf.Secrets.Ids[config.EpifiFederalCardDataPrivateKeyFallBack]})
	} else {
		rsaCryptor = epifiRsa.NewDecryptOnlyCryptor([]string{conf.Secrets.Ids[config.EpifiFederalCardDataPrivateKey]})
	}
	if rsaCryptor == nil {
		return nil, fmt.Errorf("failed to create RSA cryptor")
	}
	cryptorStore := &cryptormap.InMemoryCryptorStore{}
	cryptorStore.AddCryptor(commonvgpb.Vendor_FEDERAL_BANK, commonvgpb.CryptorType_PGP, pgpCryptor)
	cryptorStore.AddCryptor(commonvgpb.Vendor_FEDERAL_BANK, commonvgpb.CryptorType_RSA, rsaCryptor)

	// Initialising to support *federalv2.DefaultPGPSecuredExchangeV2
	vgCryptorStore := &federalv2.VgCryptorStore{}
	vgCryptorStore.AddCryptor(commonvgpb.Vendor_FEDERAL_BANK, commonvgpb.CryptorType_PGP, pgpCryptor)
	vgCryptorStore.AddCryptor(commonvgpb.Vendor_FEDERAL_BANK, commonvgpb.CryptorType_RSA, rsaCryptor)
	federalv2.StoreCryptorStoreMap(vgCryptorStore)

	// Initialising to support *federal.DefaultPGPSecuredExchange
	cryptormap.NewVendorCryptorMap(cryptorStore)

	return cryptorStore, nil
}

func InitialiseAclSftpService(gconf *genconf.Config, vendorApiGConf *vendorapiPkgGenConf.Config) (*aclvg.Service, error) {
	wire.Build(
		envProvider,
		getSftpClientForAcl,
		aclvg.NewService,
		providers.GetVendorHttpClient,
		nilSigningContextProvider,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &aclvg.Service{}, nil
}

func getSftpClientForAcl(gconf *genconf.Config) (sftp.ISftp, error) {
	aclSftpSecrets := gconf.Application().AclSftp()
	sftpClientConfig, err := sftp.NewClientConfigInsecure(aclSftpSecrets.User(), gconf.Secrets().Ids[config.AclSftpSecretKey], aclSftpSecrets.Host(), aclSftpSecrets.Port())
	if err != nil {
		return nil, err
	}
	return sftp.NewSftp(sftpClientConfig), nil
}

func InitializePaymentGatewayService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config, conf *config.Config) *pg.Service {
	client := providers.GetHttpClientForRazorpay(gconf)
	signingContext := nilSigningContextProvider()
	// todo: (Vineet) add exhaustive redaction strategy in config for payment gateway for masking amounts and bank details
	httpContentRedactor := httpcontentredactor.GetInstance()
	razorpayHttpRequestHandler := vendorapiPkg.New(client, signingContext, httpContentRedactor, vendorapiGconf, envProvider(gconf))
	service := pg.NewService(razorpayHttpRequestHandler, conf)
	return service
}

func InitializeVkycCallService(gconf *genconf.Config, vendorApiGConf *vendorapiPkgGenConf.Config) (*vkyccall.Service, error) {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		vkyccall.NewService,
	)
	return &vkyccall.Service{}, nil
}

func InitializeZendutyService(gconf *genconf.Config, vendorApiGConf *vendorapiPkgGenConf.Config) (*zenduty.Service, error) {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		zenduty.NewService,
	)
	return &zenduty.Service{}, nil
}

func InitialiseDigitapService(config *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *vgDigitapPb.Service {
	wire.Build(
		envProvider,
		digitapConfigProvider,
		SecureHttpClientNilSignCtxWireSet,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		vgDigitapPb.NewService,
	)
	return &vgDigitapPb.Service{}
}

func InitialiseDigilockerService(c *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *digilockerPerfios.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		vendorapiPkg.New,
		vendorws.NewConnectionHandler,
		vendorsse.NewConnectionHandler,
		httpcontentredactor.GetInstance,
		digilockerPerfios.NewService,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &digilockerPerfios.Service{}
}

func InitializeBillPayService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config,
	billPayVgClient billpayVgPb.BillPayServiceClient) *billpay.Service {
	wire.Build(
		providers.GetVendorHttpClient,
		nilSigningContextProvider,
		vendorApiPkgProvider,
		billpay.NewBillPaymentsFactory,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		billpay.NewService,
		setu.SetuCacheWireSet,
	)
	return &billpay.Service{}
}

func InitializeMobileRechargeService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config,
	mobileRechargeClient rechargeVgPb.MobileRechargeServiceClient) *recharge.Service {
	wire.Build(
		providers.GetVendorHttpClient,
		nilSigningContextProvider,
		vendorApiPkgProvider,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		recharge.NewService,
		setuRecharge.SetuMobileRechargeCacheWireSet,
		recharge.NewMobileRechargeFactory,
	)
	return &recharge.Service{}
}

func InitializeAaIgnosisService(gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *aaIgnosis.Service {
	wire.Build(
		envProvider,
		providers.GetVendorHttpClient,
		nilSigningContextProvider,
		aaIgnosis.NewService,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &aaIgnosis.Service{}
}

func InitalizeStockCatalogService(c *config.Config, redisClient types2.VendorgatewayRedisStore, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *bridgewise.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		types2.VendorgatewayRedisStoreRedisClientProvider,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		bridgewise.NewService,
		tokenstore2.RedisTokenStoreWireSet,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &bridgewise.Service{}
}

func InitializeNpsService(c *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *nps.Service {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		nps.NewService,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &nps.Service{}
}
func InitializeCreditCardV2Service(c *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) (*creditcard2.Service, error) {
	wire.Build(
		envProvider,
		SecureHttpClientNilSignCtxWireSet,
		creditcard2.NewService,
		vendorapiPkg.New,
		saven.NewJWTSigner,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &creditcard2.Service{}, nil
}

func InitializeFederalEscalationService(ctx context.Context, conf *config.Config, genConf *genconf.Config, config2 *vendorapiPkgGenConf.Config) *federalVg.Service {
	wire.Build(
		envProvider,
		nilSigningContextProvider,
		vendorapiPkg.New,
		federalVg.NewFederalEscalationFactory,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
		federalVg.NewService,
		providers.GetHttpClientForFederalEscalationService,
		getS3Downloader,
		wire.NewSet(attachment2.NewTicketAttachments, wire.Bind(new(attachment2.AttachmentsManager), new(*attachment2.TicketAttachments))),
	)
	return &federalVg.Service{}
}

func InitializeNuggetService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *vendorapiPkgGenConf.Config) *vgNugget.Service {
	wire.Build(
		envProvider,
		providers.GetVendorHttpClient,
		nilSigningContextProvider,
		vendorapiPkg.New,
		httpcontentredactor.GetInstance,
		vgNugget.NewService,
		wire.Bind(new(vendorapiPkg.HttpDoer), new(*http.Client)),
	)
	return &vgNugget.Service{}
}
